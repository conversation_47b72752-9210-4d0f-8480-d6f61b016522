import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    // Global defaults
    pool: 'forks',
    poolOptions: { forks: { singleFork: true } },
    testTimeout: 30000,
    hookTimeout: 15000,
    logHeapUsage: true,
    maxConcurrency: 3,
    forceRerunTriggers: ['**/package.json/**', '**/vitest.config.*/**'],
    // Explicit multi-project config (replaces deprecated workspace file usage)
    projects: [
      {
        extends: './apps/web/vitest.config.ts',
        test: {
          name: 'web',
          root: './apps/web',
          environment: 'jsdom',
          coverage: {
            provider: 'v8',
            reporter: ['text', 'text-summary', 'lcov'],
            reportsDirectory: './coverage',
          },
        },
      },
      {
        extends: './apps/bff/vitest.config.ts',
        test: {
          name: 'bff',
          root: './apps/bff',
          environment: 'node',
          coverage: {
            provider: 'v8',
            reporter: ['text', 'text-summary', 'lcov'],
            reportsDirectory: './coverage',
          },
        },
      },
      {
        test: {
          name: 'domain-bank',
          root: './packages/domain-bank',
          environment: 'node',
          coverage: {
            provider: 'v8',
            reporter: ['text', 'text-summary', 'lcov'],
            reportsDirectory: './coverage',
          },
        },
      },
      {
        test: {
          name: 'domain-ledger',
          root: './packages/domain-ledger',
          environment: 'node',
          coverage: {
            provider: 'v8',
            reporter: ['text', 'text-summary', 'lcov'],
            reportsDirectory: './coverage',
          },
        },
      },
      {
        test: {
          name: 'domain-vat',
          root: './packages/domain-vat',
          environment: 'node',
          coverage: {
            provider: 'v8',
            reporter: ['text', 'text-summary', 'lcov'],
            reportsDirectory: './coverage',
          },
        },
      },
      {
        test: {
          name: 'types',
          root: './packages/types',
          environment: 'node',
          coverage: {
            provider: 'v8',
            reporter: ['text', 'text-summary', 'lcov'],
            reportsDirectory: './coverage',
          },
        },
      },
    ],
  },
})
