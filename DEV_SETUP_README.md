# Development Environment Setup Script

The `dev-setup.sh` script provides a comprehensive solution for managing all required services in the ledgerly development environment. It can check service status, start services with proper dependency management, and stop all services cleanly.

## Quick Start

```bash
# Make the script executable (first time only)
chmod +x dev-setup.sh

# Start all development services
./dev-setup.sh

# Check status of all services
./dev-setup.sh --check

# Stop all services
./dev-setup.sh --stop
```

## Services Managed

The script manages the following services:

| Service | Port | Description | Health Check |
|---------|------|-------------|--------------|
| **frontend** | 3000 | Next.js web application | HTTP GET to localhost:3000 |
| **bff** | 4000 | Backend for Frontend API | HTTP GET to localhost:4000/health |
| **workers-py** | 8000 | FastAPI workers service | HTTP GET to localhost:8000/health |
| **celery-worker** | - | Background task processor | Process check for celery worker |
| **redis** | 6379 | Redis server for caching/queues | Redis PING command |
| **supabase** | 54321 | PostgreSQL + API services | Database connection + API check |

## Usage Examples

### Start All Services
```bash
./dev-setup.sh
```
This will:
1. Check prerequisites (pnpm, supabase, docker, etc.)
2. Set up environment variables from `.env.local`
3. Install dependencies if needed
4. Start services in dependency order
5. Wait for each service to become healthy
6. Show final status and useful URLs

### Start Specific Services
```bash
# Start only frontend and BFF
./dev-setup.sh frontend bff

# Start only the Python services
./dev-setup.sh workers-py celery-worker
```

### Check Service Status
```bash
# Basic status check
./dev-setup.sh --check

# Verbose status with health details
./dev-setup.sh --check --verbose
```

### Stop All Services
```bash
./dev-setup.sh --stop
```
This gracefully stops all services in reverse dependency order.

## Command Line Options

| Option | Short | Description |
|--------|-------|-------------|
| `--check` | `-c` | Only check service status, don't start anything |
| `--stop` | `-s` | Stop all running services |
| `--verbose` | `-v` | Enable verbose output with detailed health checks |
| `--timeout=N` | | Set timeout for service startup (default: 30s) |
| `--help` | `-h` | Show help message |

## Prerequisites

The script will check for and require:

- **pnpm**: Node.js package manager
- **supabase**: Supabase CLI for local database
- **docker**: Required for Supabase and optional Redis
- **uv**: Python package manager (optional, for workers-py)
- **redis-server**: Redis server (optional, will use Docker if not found)

### Installing Prerequisites

**macOS (using Homebrew):**
```bash
# Install pnpm
npm install -g pnpm

# Install Supabase CLI
brew install supabase/tap/supabase

# Install Docker Desktop
# Download from https://docs.docker.com/desktop/

# Install uv (for Python workers)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install Redis (optional)
brew install redis
```

**Linux:**
```bash
# Install pnpm
npm install -g pnpm

# Install Supabase CLI
# See https://supabase.com/docs/guides/cli

# Install Docker
# See https://docs.docker.com/engine/install/

# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

## Environment Setup

The script automatically:

1. Creates `.env.local` from `.env.example` if it doesn't exist
2. Loads environment variables from `.env.local`
3. Validates required environment variables

Make sure to review and update `.env.local` with your specific configuration after the first run.

## Service Dependencies

The script starts services in the correct dependency order:

1. **supabase** - Database and API foundation
2. **redis** - Caching and message broker
3. **workers-py** - Python API service
4. **celery-worker** - Background task processor (depends on Redis)
5. **bff** - Backend for Frontend (depends on Supabase)
6. **frontend** - Web application (depends on BFF)

When stopping, services are stopped in reverse order to ensure clean shutdown.

## Troubleshooting

### Service Won't Start
1. Check the log files in `.dev-logs/` directory:
   ```bash
   tail -f .dev-logs/frontend.log
   tail -f .dev-logs/bff.log
   tail -f .dev-logs/workers-py.log
   ```

2. Check if ports are already in use:
   ```bash
   lsof -i :3000  # Frontend
   lsof -i :4000  # BFF
   lsof -i :8000  # Workers-py
   ```

3. Manually kill processes on conflicting ports:
   ```bash
   kill $(lsof -ti :3000)
   ```

### Environment Issues
1. Verify `.env.local` exists and has correct values
2. Check that Supabase is running: `supabase status`
3. Verify Docker is running: `docker info`

### Permission Issues
```bash
# Make sure script is executable
chmod +x dev-setup.sh

# Check if you can write to the project directory
touch .test && rm .test
```

## Integration with Existing Workflow

This script complements the existing development setup:

- **Initial Setup**: Use `./bin/setup-local-dev` for first-time environment setup
- **Daily Development**: Use `./dev-setup.sh` to start/stop/check services
- **Manual Control**: You can still use individual commands like `pnpm dev`, `make run-dev`, etc.

## Log Files and Debugging

The script creates log files in `.dev-logs/` for each service:

- `frontend.log` - Next.js development server output
- `bff.log` - Backend for Frontend API logs
- `workers-py.log` - FastAPI workers service logs
- `celery-worker.log` - Celery worker process logs
- `redis.log` - Redis server logs
- `supabase.log` - Supabase startup logs

Process IDs are stored in `.dev-pids/` for clean shutdown management.

## Advanced Usage

### Custom Timeout
```bash
# Wait up to 60 seconds for services to start
./dev-setup.sh --timeout=60
```

### Verbose Debugging
```bash
# See detailed health check information
./dev-setup.sh --verbose
```

### Combining Options
```bash
# Check status with verbose output
./dev-setup.sh --check --verbose

# Start specific services with custom timeout
./dev-setup.sh --timeout=45 frontend bff
```

This script provides a robust, production-ready solution for managing the complex ledgerly development environment with proper error handling, health checks, and dependency management.
