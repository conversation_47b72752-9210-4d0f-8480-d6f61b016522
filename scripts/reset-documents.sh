#!/bin/bash

# Document Processing Reset Script
# 
# This script resets all documents to "unprocessed" state and cleans extracted data
# from the database. It can be used for both local development and staging environments.
# 
# Usage:
#   ./scripts/reset-documents.sh              # Reset local database
#   ./scripts/reset-documents.sh staging      # Reset staging database
#   ./scripts/reset-documents.sh prod         # Reset production database (requires confirmation)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-local}

echo -e "${BLUE}🔄 Document Processing Reset Script${NC}"
echo -e "${BLUE}===================================${NC}"

# Check if Python script exists
if [ ! -f "scripts/reset-documents.py" ]; then
    echo -e "${RED}❌ Python reset script not found at scripts/reset-documents.py${NC}"
    exit 1
fi

# Check if psycopg2 is available
if ! python3 -c "import psycopg2" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  psycopg2 not found. Installing...${NC}"
    
    # Try to install psycopg2
    if command -v pip3 &> /dev/null; then
        pip3 install psycopg2-binary
    elif command -v pip &> /dev/null; then
        pip install psycopg2-binary
    else
        echo -e "${RED}❌ pip not found. Please install psycopg2-binary manually:${NC}"
        echo -e "${RED}   pip install psycopg2-binary${NC}"
        exit 1
    fi
fi

# Validate environment
case $ENVIRONMENT in
    local|staging|prod)
        echo -e "${GREEN}✅ Environment: $ENVIRONMENT${NC}"
        ;;
    *)
        echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
        echo -e "${RED}   Valid options: local, staging, prod${NC}"
        exit 1
        ;;
esac

# Load environment variables if .env.local exists
if [ -f ".env.local" ]; then
    echo -e "${GREEN}📄 Loading environment variables from .env.local${NC}"
    export $(grep -v '^#' .env.local | xargs)
fi

# Run the Python script
echo -e "${BLUE}🚀 Running reset for $ENVIRONMENT environment...${NC}"
echo ""

if [ "$ENVIRONMENT" = "local" ]; then
    python3 scripts/reset-documents.py
elif [ "$ENVIRONMENT" = "staging" ]; then
    python3 scripts/reset-documents.py --staging
elif [ "$ENVIRONMENT" = "prod" ]; then
    python3 scripts/reset-documents.py --prod
fi

echo ""
echo -e "${GREEN}✨ Reset script completed!${NC}"

# Show next steps
if [ "$ENVIRONMENT" = "local" ]; then
    echo ""
    echo -e "${BLUE}💡 Next steps:${NC}"
    echo -e "   • Start your development servers: ${YELLOW}./dev-setup.sh${NC}"
    echo -e "   • Open the frontend: ${YELLOW}http://localhost:3000${NC}"
    echo -e "   • Upload documents to test processing"
fi
