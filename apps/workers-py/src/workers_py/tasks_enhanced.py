"""Enhanced inbox document processing task with AI extraction."""

import asyncio
import hashlib
import logging
import time
from pathlib import Path
from typing import Any

from celery import Task

from .extraction.models import DocumentContext
from .services.extract_v2 import ExtractedDocument, create_extraction_service
from .services.text_extraction import TextExtractionError, TextExtractionService
from .tasks import CallbackTask, celery_app, download_file

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, base=CallbackTask)
def process_inbox_document_enhanced_task(
    self,
    document_id: int,
    entity_id: int,
    file_url: str
) -> dict[str, Any]:
    """
    Enhanced inbox document processing with AI extraction and shadow mode.

    Args:
        document_id: Document ID from database
        entity_id: Entity ID
        file_url: Signed URL to download file

    Returns:
        Processing results with extraction data
    """
    result = {
        "document_id": document_id,
        "entity_id": entity_id,
        "status": "processing",
        "extraction": None,
        "extraction_alt": None,
        "confidence": None,
        "confidence_alt": None,
        "metadata": None,
        "error": None
    }

    temp_file_path: Path | None = None

    try:
        logger.info(f"Processing enhanced inbox document {document_id} for entity {entity_id}")
        start_time = time.time()

        # Update progress - downloading
        self.update_state(
            state="PROGRESS",
            meta={"progress": 10, "stage": "downloading", "document_id": document_id}
        )

        # Download the file
        temp_file_path = download_file(file_url)
        file_bytes = temp_file_path.read_bytes()

        # Compute file hash for deduplication
        file_hash = hashlib.sha256(file_bytes).hexdigest()

        # Update progress - text extraction
        self.update_state(
            state="PROGRESS",
            meta={"progress": 25, "stage": "text_extraction", "document_id": document_id}
        )

        # Extract text from document
        text_service = TextExtractionService()
        ocr_result, detected_mime_type = _run_async(
            text_service.extract_text_from_bytes(file_bytes)
        )

        logger.info("Extracted %s characters from %s", len(ocr_result.text), detected_mime_type)

        # Update progress - AI extraction
        self.update_state(
            state="PROGRESS",
            meta={"progress": 40, "stage": "ai_extraction", "document_id": document_id}
        )

        # Create extraction service and process document
        extraction_service = create_extraction_service(entity_id)

        # Load supplier templates (if any)
        supplier_templates = _run_async(load_supplier_templates(entity_id))

        # Process with AI extraction
        document_context = DocumentContext(ocr=ocr_result, mime_type=detected_mime_type)
        extracted_doc: ExtractedDocument = _run_async(
            extraction_service.process_document(
                document=document_context,
                entity_id=entity_id,
                supplier_templates=supplier_templates,
            )
        )

        # Update progress - saving results
        self.update_state(
            state="PROGRESS",
            meta={"progress": 85, "stage": "saving", "document_id": document_id}
        )

        # Prepare database update data
        extraction_dict = extracted_doc.primary_result.model_dump()
        extraction_alt_dict = (
            extracted_doc.shadow_result.model_dump()
            if extracted_doc.shadow_result else None
        )

        # Enhanced metadata
        enhanced_metadata = {
            "provider": extraction_service.provider,
            "run_mode": extraction_service.run_mode,
            "processing_time": extracted_doc.processing_time,
            "text_length": extracted_doc.text_length,
            "mime_type": detected_mime_type,
            "file_size": len(file_bytes),
            **extracted_doc.primary_metadata
        }

        # Update document in database
        _run_async(update_document_with_extraction(
            document_id=document_id,
            entity_id=entity_id,
            status="extracted",
            extraction=extraction_dict,
            confidence=extracted_doc.primary_confidence,
            extraction_alt=extraction_alt_dict,
            confidence_alt=extracted_doc.shadow_confidence,
            metadata=enhanced_metadata,
            file_hash=file_hash,
            text_length=len(ocr_result.text),
            processing_time=extracted_doc.processing_time,
            extraction_provider=extraction_service.provider
        ))

        # Determine if we should auto-trigger suggestion
        should_suggest = extraction_service.should_auto_suggest(extracted_doc.primary_confidence)

        # Update final result
        result.update({
            "status": "completed",
            "extraction": extraction_dict,
            "extraction_alt": extraction_alt_dict,
            "confidence": extracted_doc.primary_confidence,
            "confidence_alt": extracted_doc.shadow_confidence,
            "metadata": enhanced_metadata,
            "file_hash": file_hash,
            "should_auto_suggest": should_suggest,
            "processing_summary": extraction_service.get_extraction_summary(extracted_doc)
        })

        # Auto-trigger suggestion if confidence is high enough
        if should_suggest:
            try:
                from .suggest_task import create_suggestion_task
                suggestion_task = create_suggestion_task.delay(document_id, entity_id)
                result["suggestion_task_id"] = suggestion_task.id
                logger.info(f"Auto-triggered suggestion task: {suggestion_task.id}")
            except Exception as chain_error:
                logger.error(f"Failed to trigger suggestion task: {chain_error}")

        processing_time = time.time() - start_time
        logger.info(f"Enhanced processing completed in {processing_time:.2f}s "
                   f"(confidence: {extracted_doc.primary_confidence:.3f})")

        return result

    except TextExtractionError as e:
        logger.error(f"Text extraction failed for document {document_id}: {e}")
        error_msg = f"Text extraction failed: {str(e)}"
        return _handle_processing_error(self, document_id, entity_id, error_msg, result)

    except Exception as e:
        logger.error(f"Enhanced processing failed for document {document_id}: {e}")
        error_msg = f"Processing failed: {str(e)}"
        return _handle_processing_error(self, document_id, entity_id, error_msg, result)

    finally:
        # Clean up temporary file
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink(missing_ok=True)


async def load_supplier_templates(entity_id: int) -> dict[str, Any]:
    """Load supplier templates for entity."""
    try:
        from .database import get_database
        async with get_database() as db:
            rows = await db.fetch_all(
                """
                SELECT supplier_vat, default_account_id, default_vat_code_id, memo
                FROM supplier_templates
                WHERE entity_id = $1 AND active = true
                """,
                entity_id
            )

            return {
                row['supplier_vat']: {
                    "default_account_id": row['default_account_id'],
                    "default_vat_code_id": row['default_vat_code_id'],
                    "memo": row['memo']
                }
                for row in rows
            }
    except Exception as e:
        logger.warning(f"Failed to load supplier templates for entity {entity_id}: {e}")
        return {}


async def update_document_with_extraction(
    document_id: int,
    entity_id: int,
    status: str,
    extraction: dict,
    confidence: float,
    extraction_alt: dict = None,
    confidence_alt: float = None,
    metadata: dict = None,
    file_hash: str = None,
    text_length: int = None,
    processing_time: float = None,
    extraction_provider: str = None
):
    """Update document with extraction results."""
    from .database import get_database

    async with get_database() as db:
        await db.execute(
            """
            UPDATE inbox_documents
            SET status = $1,
                extraction = $2,
                confidence = $3,
                extraction_alt = $4,
                confidence_alt = $5,
                metadata = $6,
                file_hash = $7,
                text_length = $8,
                processing_time = $9,
                extraction_provider = $10,
                updated_at = NOW()
            WHERE id = $11 AND entity_id = $12
            """,
            status,
            extraction,
            confidence,
            extraction_alt,
            confidence_alt,
            metadata,
            file_hash,
            text_length,
            processing_time,
            extraction_provider,
            document_id,
            entity_id
        )


def _handle_processing_error(
    task_instance: Task,
    document_id: int,
    entity_id: int,
    error_msg: str,
    result: dict
) -> dict:
    """Handle processing errors consistently."""

    # Update document status in database
    async def mark_failed():
        from .database import get_database
        async with get_database() as db:
            await db.execute(
                """
                UPDATE inbox_documents
                SET status = 'failed',
                    error_msg = $1,
                    updated_at = NOW()
                WHERE id = $2 AND entity_id = $3
                """,
                error_msg,
                document_id,
                entity_id
            )

    try:
        _run_async(mark_failed())
        logger.info(f"Marked document {document_id} as failed")
    except Exception as db_error:  # pragma: no cover - defensive logging
        logger.error(f"Failed to mark document {document_id} as failed: {db_error}")

    # Update task result
    result.update({
        "status": "failed",
        "error": error_msg
    })

    # Update task state
    task_instance.update_state(
        state="FAILURE",
        meta={"error": error_msg, "document_id": document_id}
    )

    return result


def _run_async(coro):
    """Execute coroutine using a dedicated event loop."""
    loop = asyncio.new_event_loop()
    try:
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(coro)
    finally:
        try:
            loop.run_until_complete(loop.shutdown_asyncgens())
        except Exception:  # pragma: no cover - best effort cleanup
            pass
        asyncio.set_event_loop(None)
        loop.close()
