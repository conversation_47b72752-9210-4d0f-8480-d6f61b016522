# VAT Setup Implementation Guide

## Overview

This document describes the complete implementation of the VAT (Value Added Tax) setup feature for the Ledgerly application. The feature allows users to enable and configure VAT tracking for their entities through a user-friendly wizard interface.

## Architecture

### Components

1. **Frontend Components**
   - `VATSetupWizard`: 3-step wizard for VAT configuration
   - `MinimalDashboard`: Integration point with loading/error states

2. **API Endpoints**
   - Next.js API: `/api/entities/[entityId]/vat/setup`
   - BFF API: `/entities/:id/vat/setup`

3. **Data Layer**
   - DAL functions: `saveVATConfiguration`, `getVATConfiguration`
   - Database: `operating_modes.config` JSONB field

4. **Type System**
   - Comprehensive Zod schemas for validation
   - TypeScript interfaces for type safety

## Configuration Schema

### VAT Configuration Structure

```typescript
interface VATConfiguration {
  enabled: boolean
  vatNumber?: string
  filingFrequency: 'monthly' | 'quarterly'
  defaultRates: {
    standard: number    // e.g., 0.21 for 21%
    reduced1: number    // e.g., 0.12 for 12%
    reduced2: number    // e.g., 0.06 for 6%
    zero: number        // e.g., 0.00 for 0%
  }
  country: string       // ISO country code, default 'BE'
  configuredAt?: string // ISO datetime
  configuredBy?: string // User UUID
}
```

### Storage Format

VAT configuration is stored in the `operating_modes.config` JSONB field:

```json
{
  "VATEnabled": true,
  "vat": {
    "enabled": true,
    "vatNumber": "BE0123456789",
    "filingFrequency": "quarterly",
    "defaultRates": {
      "standard": 0.21,
      "reduced1": 0.12,
      "reduced2": 0.06,
      "zero": 0.00
    },
    "country": "BE",
    "configuredAt": "2024-01-15T10:30:00Z",
    "configuredBy": "user-uuid"
  }
}
```

## API Endpoints

### POST /api/entities/[entityId]/vat/setup

Configures VAT settings for an entity.

**Request Body:**
```typescript
{
  enabled: boolean
  vatNumber?: string
  filingFrequency: 'monthly' | 'quarterly'
  defaultRates: {
    standard: number
    reduced1: number
    reduced2: number
    zero: number
  }
}
```

**Response:**
```typescript
{
  success: boolean
  data?: {
    configuration: VATConfiguration
  }
  error?: string
}
```

### GET /api/entities/[entityId]/vat/setup

Retrieves current VAT configuration for an entity.

**Response:**
```typescript
{
  success: boolean
  data: {
    configuration: VATConfiguration | null
  }
  error?: string
}
```

## User Flow

### 1. Accessing VAT Setup

Users can access the VAT setup wizard through:
- Dashboard VAT metric card (when VAT not configured)
- AI copilot nudges
- Direct navigation to VAT page

### 2. Setup Wizard Steps

**Step 1: Enable VAT Tracking**
- Checkbox to enable VAT tracking
- Optional VAT number input (Belgian format: BE0123456789)
- Real-time validation

**Step 2: Filing Frequency**
- Radio buttons for monthly/quarterly filing
- Quarterly is recommended and pre-selected

**Step 3: Default VAT Rates**
- Input fields for Belgian VAT rates:
  - Standard rate (default: 21%)
  - Reduced rate 1 (default: 12%)
  - Reduced rate 2 (default: 6%)
  - Zero rate (default: 0%)

### 3. Configuration Save

- Validates all inputs
- Saves to database via API
- Updates entity operating mode
- Refreshes dashboard data
- Shows success/error feedback

## Validation Rules

### VAT Number Validation
- Optional field
- Must match Belgian format: `BE[0-9]{10}`
- Real-time validation with error display

### Rate Validation
- Must be between 0% and 100% (0.0 to 1.0)
- Validated on both client and server
- Clear error messages for invalid values

### Business Rules
- Only entity owners and admins can configure VAT
- Configuration is entity-specific
- Supports both new and legacy config formats

## Error Handling

### Client-Side Errors
- Form validation errors
- Network connectivity issues
- Permission denied scenarios

### Server-Side Errors
- Authentication failures
- Authorization issues
- Database constraint violations
- Invalid configuration data

### Error Messages
- User-friendly error descriptions
- Specific validation feedback
- Actionable error resolution steps

## Backward Compatibility

The implementation maintains backward compatibility with existing VAT configurations:

1. **Legacy Flag Support**: Existing `VATEnabled: true` flags continue to work
2. **Graceful Migration**: Legacy configs are automatically converted to new format when accessed
3. **Dual Storage**: Both legacy and new formats are maintained during transition

## Testing

### Unit Tests
- Type validation schemas
- Helper functions
- Configuration merging logic
- VAT number validation

### Integration Tests
- API endpoint functionality
- Database operations
- Error handling scenarios
- Authentication/authorization

### End-to-End Tests
- Complete wizard flow
- Configuration persistence
- Dashboard integration
- Error recovery

## Troubleshooting

### Common Issues

**VAT Setup Button Not Appearing**
- Check entity selection
- Verify user permissions (owner/admin required)
- Ensure entity has operating mode configured

**Configuration Not Saving**
- Check network connectivity
- Verify authentication status
- Check browser console for errors
- Validate input data format

**VAT Number Validation Errors**
- Ensure format is BE followed by 10 digits
- Check for extra spaces or characters
- Verify Belgian VAT number requirements

**Permission Denied Errors**
- Confirm user has owner or admin role for entity
- Check entity membership status
- Verify authentication token validity

### Debug Information

Enable debug logging by checking browser console for:
- API request/response details
- Validation error specifics
- Authentication status
- Entity context information

## Security Considerations

1. **Authentication**: All endpoints require valid user authentication
2. **Authorization**: Only entity owners/admins can configure VAT
3. **Input Validation**: Comprehensive validation on both client and server
4. **Data Sanitization**: All inputs are sanitized before storage
5. **Audit Trail**: Configuration changes are logged with user and timestamp

## Performance Considerations

1. **Lazy Loading**: VAT configuration is loaded only when needed
2. **Caching**: Configuration is cached in component state
3. **Optimistic Updates**: UI updates immediately with rollback on error
4. **Minimal Payloads**: Only necessary data is transmitted

## Future Enhancements

1. **Multi-Country Support**: Extend beyond Belgian VAT
2. **Rate History**: Track VAT rate changes over time
3. **Automated Filing**: Integration with tax authorities
4. **Advanced Validation**: Real-time VAT number verification
5. **Bulk Configuration**: Configure multiple entities simultaneously

## Implementation Files

### Type Definitions
- `packages/types/src/vat-config.ts` - Complete type system and validation schemas
- `packages/types/src/vat-config.test.ts` - Comprehensive unit tests

### Data Access Layer
- `packages/dal/src/vat.ts` - VAT configuration save/retrieve functions
- `packages/dal/src/index.ts` - Exported DAL functions

### API Endpoints
- `apps/web/app/api/entities/[entityId]/vat/setup/route.ts` - Next.js API route
- `apps/bff/src/routes/vat.ts` - BFF endpoints

### Frontend Components
- `apps/web/components/VATSetupWizard.tsx` - Setup wizard component
- `apps/web/components/MinimalDashboard.tsx` - Dashboard integration

## Related Documentation

- [VAT Management API](./vat-api.md)
- [Operating Modes Configuration](./operating-modes.md)
- [Entity Management](./entity-management.md)
- [Authentication & Authorization](./auth.md)