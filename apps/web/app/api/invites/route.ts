import { NextRequest } from 'next/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'
import { createInvite, listSentInvites } from '@belbooks/dal'
import { validateCSRFMiddleware } from '@/lib/csrf'
import { SecurityEvents } from '@/lib/security-monitoring'
import { getClientIP } from '@/lib/rate-limit'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type {
  InviteResponse,
  InviteListResponse,
  ErrorResponse,
} from '@/lib/api-types'
import { isInviteRequest } from '@/lib/api-types'
import { createSecureServerClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest): Promise<Response> {
  try {
    // CSRF validation
    const csrfValidation = await validateCSRFMiddleware(request)
    if (!csrfValidation) {
      void SecurityEvents.suspiciousRequest(
        'unknown',
        'CSRF validation failed',
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown',
        { endpoint: '/api/invites' }
      )

      return createJsonResponse<ErrorResponse>(
        { error: 'Invalid CSRF token' },
        403
      )
    }

    // Get authenticated user
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    // Parse and validate request body
    const body: unknown = await request.json()

    if (!isInviteRequest(body)) {
      return createJsonResponse<ErrorResponse>(
        {
          error:
            'Invalid request body. Required fields: scope, scopeId, email, role',
        },
        400
      )
    }

    const { scope, scopeId, email, role } = body

    // Validate role based on scope
    const validTenantRoles = [
      'tenant_owner',
      'tenant_admin',
      'tenant_billing',
      'tenant_member',
    ]
    const validEntityRoles = [
      'owner',
      'admin',
      'accountant',
      'bookkeeper',
      'viewer',
    ]

    if (scope === 'tenant' && !validTenantRoles.includes(role)) {
      return createJsonResponse<ErrorResponse>(
        {
          error: `Invalid tenant role. Must be one of: ${validTenantRoles.join(', ')}`,
        },
        400
      )
    }

    if (scope === 'entity' && !validEntityRoles.includes(role)) {
      return createJsonResponse<ErrorResponse>(
        {
          error: `Invalid entity role. Must be one of: ${validEntityRoles.join(', ')}`,
        },
        400
      )
    }

    // Create invitation using DAL
    try {
      const scopeIdString =
        typeof scopeId === 'number' ? scopeId.toString() : scopeId
      const inviteToken = await createInvite(scope, scopeIdString, email, role)

      // Log invitation creation
      void SecurityEvents.adminAction(
        user.id,
        'create_invitation',
        `${scope}:${scopeIdString}:${email}`,
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown'
      )

      return createJsonResponse<InviteResponse>({
        success: true,
        inviteToken: inviteToken || undefined,
        message: inviteToken
          ? 'Invitation sent'
          : 'User already exists and was granted access directly',
      })
    } catch (error) {
      console.error('Error creating invitation:', error)

      const scopeIdString =
        typeof scopeId === 'number' ? scopeId.toString() : scopeId
      // Log failed invitation attempt
      void SecurityEvents.adminAction(
        user.id,
        'create_invitation_failed',
        `${scope}:${scopeIdString}:${email}`,
        getClientIP(request),
        request.headers.get('user-agent') || 'unknown'
      )

      return createJsonResponse<ErrorResponse>(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create invitation',
        },
        400
      )
    }
  } catch (error) {
    console.error('Invitation API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}

export async function GET(_request: NextRequest): Promise<Response> {
  try {
    // Get authenticated user
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    // Get sent invitations
    const invitedRows: unknown[] = (await listSentInvites()) as unknown[]
    const invites = invitedRows.map(raw => {
      const inv = raw as Record<string, unknown>
      return {
        created_at: String(inv.created_at),
        email: String(inv.email),
        expires_at: String(inv.expires_at),
        inviter_user_id: String(inv.inviter_user_id),
        role: String(inv.role),
        scope: String(inv.scope),
        scope_id: String(inv.scope_id),
        token: String(inv.token),
      }
    })
    return createJsonResponse<InviteListResponse>({ invites })
  } catch (error) {
    console.error('Error fetching invitations:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}
