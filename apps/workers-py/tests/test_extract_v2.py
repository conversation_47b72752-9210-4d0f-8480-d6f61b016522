"""Tests for EnhancedExtractionService using DocumentContext inputs."""

from unittest.mock import AsyncMock, Mock, patch

import pytest

from workers_py.adapters.base import ExtractionError
from workers_py.adapters.gemini_flash import GeminiFlashAdapter
from workers_py.models import (
    ExtractionResult,
    InvoiceInfo,
    PaymentInstructions,
    SupplierInfo,
    TotalsInfo,
)
from workers_py.services.extract_v2 import ExtractedDocument, EnhancedExtractionService


class TestEnhancedExtractionService:
    """Test suite covering the async extraction orchestration."""

    @pytest.fixture
    def service(self) -> EnhancedExtractionService:
        return EnhancedExtractionService(entity_id=123)

    async def test_process_document_success(self, service, sample_document_context):
        mock_result = ExtractionResult(
            supplier=SupplierInfo(name="Test Company BVBA", vat="BE0123456789"),
            invoice=InvoiceInfo(
                number="2024-INV-123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00",
            ),
            totals=TotalsInfo(net="100.00", vat="21.00", gross="121.00", currency="EUR"),
            payment_instructions=PaymentInstructions(iban="****************"),
            lines=[],
        )

        service.primary_adapter.extract = AsyncMock(
            return_value=(mock_result, 0.85, {"provider": "gemini_flash"})
        )

        result = await service.process_document(sample_document_context, entity_id=123)

        assert isinstance(result, ExtractedDocument)
        assert result.primary_result == mock_result
        assert result.primary_confidence == 0.85
        assert result.primary_metadata["provider"] == "gemini_flash"
        assert result.text_length == len(sample_document_context.ocr.text)
        assert result.processing_time > 0

    async def test_process_document_empty_text(self, service, empty_document_context):
        with pytest.raises(ExtractionError, match="Empty text provided"):
            await service.process_document(empty_document_context, entity_id=123)

    async def test_process_document_with_shadow_mode(
        self, service, sample_document_context, mock_extraction_result
    ):
        service.run_mode = "shadow"
        service.shadow_adapter = Mock()
        service.shadow_adapter.extract = AsyncMock(
            return_value=(mock_extraction_result, 0.75, {"provider": "stub"})
        )
        service.primary_adapter.extract = AsyncMock(
            return_value=(mock_extraction_result, 0.85, {"provider": "gemini_flash"})
        )

        result = await service.process_document(sample_document_context, entity_id=123)

        assert result.shadow_result == mock_extraction_result
        assert result.shadow_confidence == 0.75
        assert result.shadow_metadata["provider"] == "stub"

    async def test_process_document_with_retry(
        self, service, sample_document_context, mock_extraction_result
    ):
        service.primary_adapter.extract = AsyncMock(
            side_effect=[
                ExtractionError("Temporary failure"),
                (mock_extraction_result, 0.85, {"provider": "gemini_flash"}),
            ]
        )

        result = await service.process_document(sample_document_context, entity_id=123)

        assert result.primary_result == mock_extraction_result
        assert service.primary_adapter.extract.call_count == 2

    def test_create_adapter_gemini_langextract(self, service):
        from workers_py.adapters.gemini_langextract import GeminiLangExtractAdapter

        adapter = service._create_adapter("gemini_langextract")
        assert isinstance(adapter, GeminiLangExtractAdapter)

    def test_create_adapter_gemini_flash(self, service):
        adapter = service._create_adapter("gemini_flash")
        assert isinstance(adapter, GeminiFlashAdapter)

    def test_create_adapter_stub(self, service):
        from workers_py.adapters.stub import StubExtractionAdapter

        adapter = service._create_adapter("stub")
        assert isinstance(adapter, StubExtractionAdapter)

    def test_create_adapter_unknown(self, service):
        with pytest.raises(ValueError, match="Unknown extraction provider"):
            service._create_adapter("unknown_provider")


class TestExtractedDocument:
    """Tests for the ExtractedDocument container class."""

    def test_extracted_document_creation(self):
        result = ExtractionResult(
            supplier=SupplierInfo(name="Test Company"),
            invoice=InvoiceInfo(
                number="123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00",
            ),
            totals=TotalsInfo(net="100.00", vat="21.00", gross="121.00", currency="EUR"),
            payment_instructions=PaymentInstructions(iban="****************"),
            lines=[],
        )

        doc = ExtractedDocument(
            primary_result=result,
            primary_confidence=0.85,
            primary_metadata={"provider": "test"},
            text_length=100,
            processing_time=1.5,
        )

        assert doc.primary_result == result
        assert doc.primary_confidence == 0.85
        assert doc.primary_metadata["provider"] == "test"
        assert doc.text_length == 100
        assert doc.processing_time == 1.5
        assert doc.shadow_result is None
        assert doc.shadow_confidence is None
        assert doc.shadow_metadata is None

    def test_extracted_document_with_shadow(self):
        primary_result = ExtractionResult(
            supplier=SupplierInfo(name="Primary Company"),
            invoice=InvoiceInfo(
                number="123",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00",
            ),
            totals=TotalsInfo(net="100.00", vat="21.00", gross="121.00", currency="EUR"),
            payment_instructions=PaymentInstructions(iban="****************"),
            lines=[],
        )
        shadow_result = ExtractionResult(
            supplier=SupplierInfo(name="Shadow Company"),
            invoice=InvoiceInfo(
                number="456",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00",
            ),
            totals=TotalsInfo(net="100.00", vat="21.00", gross="121.00", currency="EUR"),
            payment_instructions=PaymentInstructions(iban="****************"),
            lines=[],
        )

        doc = ExtractedDocument(
            primary_result=primary_result,
            primary_confidence=0.85,
            primary_metadata={"provider": "gemini"},
            shadow_result=shadow_result,
            shadow_confidence=0.75,
            shadow_metadata={"provider": "stub"},
            text_length=100,
            processing_time=1.5,
        )

        assert doc.primary_result == primary_result
        assert doc.shadow_result == shadow_result
        assert doc.primary_confidence == 0.85
        assert doc.shadow_confidence == 0.75
