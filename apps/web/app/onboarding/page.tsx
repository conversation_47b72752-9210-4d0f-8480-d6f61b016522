'use client'

import { useState, useEffect, CSSProperties } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import {
  getPendingInvitesForUser,
  listUserTenants,
  createTenantRPC,
} from '@belbooks/dal'
import { parseJsonSafe, getApiErrorMessage } from '@/lib/http/api'
import { useCSRF } from '@/hooks/useCSRF'

// Zen UI Theme - Following the style guide exactly
const zenTheme = {
  // Primary Colors - CORRECTED to match style guide
  bg: '#FBFAF5', // soft cream background (was wrong before)
  surface: '#FFFFFC', // pure white for cards
  primaryText: '#1a1a1a', // near black
  secondaryText: '#6b7280', // warm gray
  subtleText: '#9ca3af', // light gray

  // Borders and Surfaces
  border: '#f3f4f6', // subtle border
  borderHover: '#e5e7eb', // slightly darker on hover

  // Accent Colors (use sparingly)
  success: '#10b981', // soft green
  warning: '#f59e0b', // warm amber
  error: '#ef4444', // soft red
  primaryAction: '#3b82f6', // calm blue (not black for primary action)

  // Shadows and Effects
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)', // very subtle
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', // slightly more on hover
}

// Zen UI Style Functions
const getPageStyle = (): CSSProperties => ({
  minHeight: '100vh',
  background: zenTheme.bg,
  fontFamily:
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
  lineHeight: 1.5,
  padding: '48px 24px', // generous whitespace
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
})

const getCardStyle = (): CSSProperties => ({
  background: zenTheme.surface,
  border: `1px solid ${zenTheme.border}`,
  borderRadius: '8px',
  padding: '48px', // generous padding
  maxWidth: '480px', // slightly wider for better readability
  width: '100%',
  boxShadow: zenTheme.shadow,
})

const getHeadingStyle = (size: 'h1' | 'h2' = 'h1'): CSSProperties => ({
  fontSize: size === 'h1' ? '24px' : '20px',
  fontWeight: 600,
  color: zenTheme.primaryText,
  margin: '0 0 8px 0',
  lineHeight: size === 'h1' ? 1.3 : 1.4,
})

const getSubtitleStyle = (): CSSProperties => ({
  fontSize: '14px',
  color: zenTheme.secondaryText,
  margin: '0 0 32px 0',
})

const getPrimaryButtonStyle = (disabled = false): CSSProperties => ({
  width: '100%',
  padding: '12px 24px',
  background: disabled ? zenTheme.subtleText : zenTheme.primaryAction,
  color: '#FFFFFF',
  border: 'none',
  borderRadius: '6px',
  fontSize: '14px',
  fontWeight: 500,
  cursor: disabled ? 'not-allowed' : 'pointer',
  transition: 'all 0.15s ease',
  opacity: disabled ? 0.6 : 1,
})

const getSecondaryButtonStyle = (): CSSProperties => ({
  background: 'transparent',
  color: zenTheme.secondaryText,
  border: 'none',
  fontSize: '14px',
  fontWeight: 500,
  cursor: 'pointer',
  transition: 'all 0.15s ease',
  textDecoration: 'underline',
})

const getInputStyle = (): CSSProperties => ({
  width: '100%',
  padding: '12px 16px',
  border: `1px solid ${zenTheme.border}`,
  borderRadius: '6px',
  background: zenTheme.surface,
  color: zenTheme.primaryText,
  fontSize: '14px',
  outline: 'none',
  transition: 'all 0.15s ease',
})

const getLabelStyle = (): CSSProperties => ({
  display: 'block',
  fontSize: '14px',
  fontWeight: 500,
  color: zenTheme.primaryText,
  marginBottom: '8px',
})

const getErrorStyle = (): CSSProperties => ({
  background: '#fef2f2',
  border: `1px solid ${zenTheme.error}`,
  borderRadius: '6px',
  padding: '16px',
  marginBottom: '24px',
})

const getErrorTextStyle = (): CSSProperties => ({
  fontSize: '14px',
  color: zenTheme.error,
  margin: 0,
})

type PendingInvite = {
  token: string
  scope: 'tenant' | 'entity'
  scope_id: string
  email: string
  role: string
  expires_at: string
}

type TenantCreationStep = {
  name: string
  kind: 'SME' | 'FIRM'
}

type Tenant = {
  id: number
  name: string
  description: string | null
  created_by: string
  created_at: string
  role: string
}

export default function OnboardingPage() {
  // Add CSS animation for spinner
  if (
    typeof document !== 'undefined' &&
    !document.querySelector('#zen-spinner-styles')
  ) {
    const style = document.createElement('style')
    style.id = 'zen-spinner-styles'
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `
    document.head.appendChild(style)
  }

  const [loading, setLoading] = useState(true)
  const [pendingInvites, setPendingInvites] = useState<PendingInvite[]>([])
  const [currentStep, setCurrentStep] = useState<
    'checking' | 'invites' | 'create-tenant' | 'complete'
  >('checking')
  const [tenantForm, setTenantForm] = useState<TenantCreationStep>({
    name: '',
    kind: 'SME',
  })
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState('')

  const router = useRouter()
  const { user } = useAuth()
  const { /* csrfToken, */ addCSRFHeaders } = useCSRF()

  useEffect(() => {
    if (!user?.email) return

    const checkOnboardingStatus = async (): Promise<void> => {
      try {
        setLoading(true)

        // 1) Check for existing tenant memberships
        const tenants: Tenant[] =
          (await listUserTenants()) as unknown as Tenant[]
        if (tenants && tenants.length > 0) {
          setCurrentStep('complete')
          router.replace('/')
          return
        }

        // 2) Check for pending invites by user email
        const email = user.email as string
        const invites = await getPendingInvitesForUser(email)
        setPendingInvites(invites as unknown as PendingInvite[])
        setCurrentStep(invites.length > 0 ? 'invites' : 'create-tenant')
      } catch (err) {
        console.error('Onboarding check error:', err)
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to check onboarding status'
        )
        setCurrentStep('create-tenant')
      } finally {
        setLoading(false)
      }
    }

    void checkOnboardingStatus()
  }, [user?.email, router])

  const handleAcceptInvite = (token: string): void => {
    setProcessing(true)
    setError('')

    const run = async () => {
      try {
        // Prefer our API route to include CSRF; DAL also works but we keep parity
        const resp = await fetch('/api/invites/accept', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...addCSRFHeaders(),
          },
          body: JSON.stringify({ token }),
        })
        const data = await parseJsonSafe<{ success?: boolean; error?: string }>(
          resp
        )
        if (!resp.ok || !data || data.success !== true) {
          throw new Error(
            getApiErrorMessage(data, 'Failed to accept invitation')
          )
        }
        // After accepting, go to dashboard; middleware may redirect if needed
        router.replace('/')
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to accept invitation'
        )
      } finally {
        setProcessing(false)
      }
    }

    void run()
  }

  const handleCreateTenant = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    setProcessing(true)
    setError('')

    try {
      if (!tenantForm.name.trim()) {
        throw new Error('Tenant name is required')
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      await createTenantRPC(
        tenantForm.name.trim(),
        tenantForm.kind.toLowerCase() as 'sme' | 'firm'
      )

      // Success - redirect to dashboard
      router.replace('/')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create tenant')
    } finally {
      setProcessing(false)
    }
  }

  if (!user) {
    router.replace('/login')
    return null
  }

  if (loading || currentStep === 'checking') {
    return (
      <div style={getPageStyle()}>
        <div style={{ textAlign: 'center' }}>
          <div
            style={{
              width: '32px',
              height: '32px',
              border: `2px solid ${zenTheme.border}`,
              borderTop: `2px solid ${zenTheme.primaryAction}`,
              borderRadius: '50%',
              margin: '0 auto 16px',
              animation: 'spin 1s linear infinite',
            }}
          ></div>
          <p
            style={{
              color: zenTheme.secondaryText,
              fontSize: '14px',
              margin: 0,
            }}
          >
            Setting up your account...
          </p>
        </div>
      </div>
    )
  }

  if (currentStep === 'complete') {
    return (
      <div style={getPageStyle()}>
        <div style={{ textAlign: 'center' }}>
          <p
            style={{
              color: zenTheme.secondaryText,
              fontSize: '14px',
              margin: 0,
            }}
          >
            Redirecting to dashboard...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div style={getPageStyle()}>
      <div style={getCardStyle()}>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={getHeadingStyle('h1')}>Welcome to BelBooks!</h1>
          <p style={getSubtitleStyle()}>Let&apos;s set up your account</p>
        </div>

        {error && (
          <div style={getErrorStyle()}>
            <p style={getErrorTextStyle()}>{error}</p>
          </div>
        )}

        {currentStep === 'invites' && (
          <div
            style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}
          >
            <div>
              <h2 style={getHeadingStyle('h2')}>
                You have pending invitations
              </h2>
              <p style={{ ...getSubtitleStyle(), marginBottom: '24px' }}>
                Accept an invitation to join an existing organization, or skip
                to create your own.
              </p>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '16px',
                }}
              >
                {pendingInvites.map(invite => (
                  <div
                    key={invite.token}
                    style={{
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      padding: '20px',
                      background: zenTheme.surface,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                      }}
                    >
                      <div>
                        <p
                          style={{
                            fontWeight: 500,
                            color: zenTheme.primaryText,
                            margin: '0 0 8px 0',
                          }}
                        >
                          {invite.scope === 'tenant'
                            ? 'Organization'
                            : 'Entity'}{' '}
                          Invitation
                        </p>
                        <p
                          style={{
                            fontSize: '14px',
                            color: zenTheme.secondaryText,
                            margin: '0 0 4px 0',
                          }}
                        >
                          Role: {invite.role}
                        </p>
                        <p
                          style={{
                            fontSize: '12px',
                            color: zenTheme.subtleText,
                            margin: 0,
                          }}
                        >
                          Expires:{' '}
                          {new Date(invite.expires_at).toLocaleDateString()}
                        </p>
                      </div>
                      <button
                        onClick={() => handleAcceptInvite(invite.token)}
                        disabled={processing}
                        style={{
                          padding: '8px 16px',
                          background: processing
                            ? zenTheme.subtleText
                            : zenTheme.primaryAction,
                          color: '#FFFFFF',
                          border: 'none',
                          borderRadius: '6px',
                          fontSize: '14px',
                          fontWeight: 500,
                          cursor: processing ? 'not-allowed' : 'pointer',
                          opacity: processing ? 0.6 : 1,
                        }}
                      >
                        Accept
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <p
                style={{
                  fontSize: '14px',
                  color: zenTheme.secondaryText,
                  margin: '0 0 16px 0',
                }}
              >
                Don&apos;t see your invitation?
              </p>
              <button
                onClick={() => setCurrentStep('create-tenant')}
                style={getSecondaryButtonStyle()}
              >
                Create your own organization instead
              </button>
            </div>
          </div>
        )}

        {currentStep === 'create-tenant' && (
          <form
            onSubmit={e => {
              void handleCreateTenant(e)
            }}
            style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}
          >
            <div>
              <h2 style={getHeadingStyle('h2')}>Create your organization</h2>
              <p style={{ ...getSubtitleStyle(), marginBottom: '24px' }}>
                Set up your company in BelBooks. You&apos;ll be the owner and
                can invite team members later.
              </p>

              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '24px',
                }}
              >
                <div>
                  <label htmlFor="name" style={getLabelStyle()}>
                    Organization name
                  </label>
                  <input
                    id="name"
                    type="text"
                    required
                    value={tenantForm.name}
                    onChange={e =>
                      setTenantForm(prev => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    style={getInputStyle()}
                    placeholder="Enter your organization name"
                  />
                </div>

                <div>
                  <label style={getLabelStyle()}>Organization type</label>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '16px',
                    }}
                  >
                    <label
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        cursor: 'pointer',
                      }}
                    >
                      <input
                        type="radio"
                        name="kind"
                        value="SME"
                        checked={tenantForm.kind === 'SME'}
                        onChange={e =>
                          setTenantForm(prev => ({
                            ...prev,
                            kind: e.target.value as 'SME' | 'FIRM',
                          }))
                        }
                        style={{ marginRight: '12px', marginTop: '2px' }}
                      />
                      <div>
                        <span
                          style={{
                            fontWeight: 500,
                            color: zenTheme.primaryText,
                            display: 'block',
                            marginBottom: '4px',
                          }}
                        >
                          Small/Medium Enterprise (SME)
                        </span>
                        <p
                          style={{
                            fontSize: '14px',
                            color: zenTheme.secondaryText,
                            margin: 0,
                          }}
                        >
                          For individual businesses and small companies
                        </p>
                      </div>
                    </label>

                    <label
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        cursor: 'pointer',
                      }}
                    >
                      <input
                        type="radio"
                        name="kind"
                        value="FIRM"
                        checked={tenantForm.kind === 'FIRM'}
                        onChange={e =>
                          setTenantForm(prev => ({
                            ...prev,
                            kind: e.target.value as 'SME' | 'FIRM',
                          }))
                        }
                        style={{ marginRight: '12px', marginTop: '2px' }}
                      />
                      <div>
                        <span
                          style={{
                            fontWeight: 500,
                            color: zenTheme.primaryText,
                            display: 'block',
                            marginBottom: '4px',
                          }}
                        >
                          Accounting Firm
                        </span>
                        <p
                          style={{
                            fontSize: '14px',
                            color: zenTheme.secondaryText,
                            margin: 0,
                          }}
                        >
                          For accounting firms managing multiple clients
                        </p>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={processing || !tenantForm.name.trim()}
              style={getPrimaryButtonStyle(
                processing || !tenantForm.name.trim()
              )}
            >
              {processing ? 'Creating...' : 'Create Organization'}
            </button>
          </form>
        )}
      </div>
    </div>
  )
}
