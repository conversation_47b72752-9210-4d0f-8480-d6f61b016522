'use client'

import React, { useState } from 'react'

// Belgian VAT number validation
const BELGIAN_VAT_REGEX = /^BE[0-9]{10}$/

function validateBelgianVATNumber(vatNumber: string): boolean {
  if (!vatNumber) return true // Optional field
  return BELGIAN_VAT_REGEX.test(vatNumber)
}

interface VATSetupWizardProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (config: VATConfig) => void | Promise<void>
  loading?: boolean
  error?: string | null
  existingConfig?: any
  className?: string
}

interface VATConfig {
  enabled: boolean
  vatNumber?: string
  filingFrequency: 'monthly' | 'quarterly'
  defaultRates: {
    standard: number
    reduced1: number
    reduced2: number
    zero: number
  }
}

const zenTheme = {
  surface: '#FFFFFF',
  border: '#E5E7EB',
  primaryText: '#0B0F14',
  secondaryText: '#475569',
  accent: '#111827',
  accentBlue: '#2563EB',
  success: '#059669',
  shadow: '0 1px 2px rgba(0, 0, 0, 0.08)',
}

export default function VATSetupWizard({
  isOpen,
  onClose,
  onComplete,
  loading = false,
  error = null,
  existingConfig = null,
  className = '',
}: VATSetupWizardProps) {
  const [step, setStep] = useState(1)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [config, setConfig] = useState<VATConfig>(() => {
    // Initialize with existing config if available, otherwise use defaults
    if (existingConfig) {
      return {
        enabled: existingConfig.enabled || true,
        vatNumber: existingConfig.vatNumber || '',
        filingFrequency: existingConfig.filingFrequency || 'quarterly',
        defaultRates: {
          standard: existingConfig.defaultRates?.standard || 0.21,
          reduced1: existingConfig.defaultRates?.reduced1 || 0.12,
          reduced2: existingConfig.defaultRates?.reduced2 || 0.06,
          zero: existingConfig.defaultRates?.zero || 0.00,
        },
      }
    }

    return {
      enabled: true,
      vatNumber: '',
      filingFrequency: 'quarterly',
      defaultRates: {
        standard: 0.21,
        reduced1: 0.12,
        reduced2: 0.06,
        zero: 0.00,
      },
    }
  })

  if (!isOpen) return null

  const validateCurrentStep = (): boolean => {
    const errors: Record<string, string> = {}

    if (step === 1) {
      if (config.vatNumber && !validateBelgianVATNumber(config.vatNumber)) {
        errors.vatNumber = 'VAT number must be in format BE0123456789'
      }
    }

    if (step === 3) {
      // Validate rates are reasonable
      if (config.defaultRates.standard < 0 || config.defaultRates.standard > 1) {
        errors.standardRate = 'Standard rate must be between 0% and 100%'
      }
      if (config.defaultRates.reduced1 < 0 || config.defaultRates.reduced1 > 1) {
        errors.reduced1Rate = 'Reduced rate 1 must be between 0% and 100%'
      }
      if (config.defaultRates.reduced2 < 0 || config.defaultRates.reduced2 > 1) {
        errors.reduced2Rate = 'Reduced rate 2 must be between 0% and 100%'
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleNext = async () => {
    if (!validateCurrentStep()) {
      return
    }

    if (step < 3) {
      setStep(step + 1)
      setValidationErrors({}) // Clear errors when moving to next step
    } else {
      await onComplete(config)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const getOverlayStyle = () => ({
    position: 'fixed' as const,
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    padding: '16px',
  })

  const getModalStyle = () => ({
    background: zenTheme.surface,
    borderRadius: '12px',
    padding: '32px',
    width: '100%',
    maxWidth: '500px',
    maxHeight: '90vh',
    overflowY: 'auto' as const,
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
  })

  const getHeaderStyle = () => ({
    marginBottom: '24px',
    textAlign: 'center' as const,
  })

  const getTitleStyle = () => ({
    fontSize: '24px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    margin: '0 0 8px 0',
  })

  const getSubtitleStyle = () => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: 0,
  })

  const getStepIndicatorStyle = () => ({
    display: 'flex',
    justifyContent: 'center',
    gap: '8px',
    marginBottom: '32px',
  })

  const getStepDotStyle = (isActive: boolean, isCompleted: boolean) => ({
    width: '12px',
    height: '12px',
    borderRadius: '50%',
    backgroundColor: isCompleted ? zenTheme.success : isActive ? zenTheme.accentBlue : '#D1D5DB',
    transition: 'background-color 0.2s ease',
  })

  const getFormGroupStyle = () => ({
    marginBottom: '24px',
  })

  const getLabelStyle = () => ({
    display: 'block',
    fontSize: '14px',
    fontWeight: 500,
    color: zenTheme.primaryText,
    marginBottom: '8px',
  })

  const getInputStyle = () => ({
    width: '100%',
    padding: '12px',
    border: `1px solid ${zenTheme.border}`,
    borderRadius: '8px',
    fontSize: '14px',
    color: zenTheme.primaryText,
    backgroundColor: zenTheme.surface,
  })

  const getSelectStyle = () => ({
    ...getInputStyle(),
    cursor: 'pointer',
  })

  const getButtonGroupStyle = () => ({
    display: 'flex',
    gap: '12px',
    justifyContent: 'flex-end',
    marginTop: '32px',
  })

  const getButtonStyle = (variant: 'primary' | 'secondary') => ({
    padding: '12px 24px',
    borderRadius: '8px',
    fontSize: '14px',
    fontWeight: 500,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: variant === 'primary' ? 'none' : `1px solid ${zenTheme.border}`,
    backgroundColor: variant === 'primary' ? zenTheme.accentBlue : 'transparent',
    color: variant === 'primary' ? 'white' : zenTheme.primaryText,
  })

  const renderStep1 = () => (
    <div>
      <h3 style={{ fontSize: '18px', fontWeight: 600, marginBottom: '16px' }}>
        Enable VAT Tracking
      </h3>
      <p style={{ color: zenTheme.secondaryText, marginBottom: '24px' }}>
        BelBooks can help you track your VAT position in real-time and ensure compliance with Belgian VAT requirements.
      </p>
      
      <div style={getFormGroupStyle()}>
        <label style={getLabelStyle()}>
          <input
            type="checkbox"
            checked={config.enabled}
            onChange={(e) => setConfig({ ...config, enabled: e.target.checked })}
            style={{ marginRight: '8px' }}
          />
          Enable VAT tracking for this entity
        </label>
      </div>

      <div style={getFormGroupStyle()}>
        <label style={getLabelStyle()}>VAT Number (optional)</label>
        <input
          type="text"
          value={config.vatNumber}
          onChange={(e) => {
            setConfig({ ...config, vatNumber: e.target.value })
            // Clear validation error when user starts typing
            if (validationErrors.vatNumber) {
              setValidationErrors({ ...validationErrors, vatNumber: '' })
            }
          }}
          placeholder="BE0123456789"
          style={{
            ...getInputStyle(),
            borderColor: validationErrors.vatNumber ? '#ef4444' : undefined,
          }}
          disabled={!config.enabled}
        />
        {validationErrors.vatNumber && (
          <p style={{
            color: '#ef4444',
            fontSize: '12px',
            margin: '4px 0 0 0',
          }}>
            {validationErrors.vatNumber}
          </p>
        )}
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div>
      <h3 style={{ fontSize: '18px', fontWeight: 600, marginBottom: '16px' }}>
        Filing Frequency
      </h3>
      <p style={{ color: zenTheme.secondaryText, marginBottom: '24px' }}>
        Choose how often you file VAT returns. Most Belgian businesses file quarterly.
      </p>
      
      <div style={getFormGroupStyle()}>
        <label style={getLabelStyle()}>Filing Frequency</label>
        <select
          value={config.filingFrequency}
          onChange={(e) => setConfig({ ...config, filingFrequency: e.target.value as 'monthly' | 'quarterly' })}
          style={getSelectStyle()}
        >
          <option value="quarterly">Quarterly (recommended)</option>
          <option value="monthly">Monthly</option>
        </select>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div>
      <h3 style={{ fontSize: '18px', fontWeight: 600, marginBottom: '16px' }}>
        VAT Rates
      </h3>
      <p style={{ color: zenTheme.secondaryText, marginBottom: '24px' }}>
        These are the standard Belgian VAT rates. You can adjust them if needed.
      </p>
      
      <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: '1fr 1fr' }}>
        <div>
          <label style={getLabelStyle()}>Standard Rate (%)</label>
          <input
            type="number"
            value={config.defaultRates.standard}
            onChange={(e) => setConfig({
              ...config,
              defaultRates: { ...config.defaultRates, standard: Number(e.target.value) }
            })}
            style={getInputStyle()}
          />
        </div>
        <div>
          <label style={getLabelStyle()}>Reduced Rate 1 (%)</label>
          <input
            type="number"
            value={config.defaultRates.reduced1}
            onChange={(e) => setConfig({
              ...config,
              defaultRates: { ...config.defaultRates, reduced1: Number(e.target.value) }
            })}
            style={getInputStyle()}
          />
        </div>
        <div>
          <label style={getLabelStyle()}>Reduced Rate 2 (%)</label>
          <input
            type="number"
            value={config.defaultRates.reduced2}
            onChange={(e) => setConfig({
              ...config,
              defaultRates: { ...config.defaultRates, reduced2: Number(e.target.value) }
            })}
            style={getInputStyle()}
          />
        </div>
        <div>
          <label style={getLabelStyle()}>Zero Rate (%)</label>
          <input
            type="number"
            value={config.defaultRates.zero}
            onChange={(e) => setConfig({
              ...config,
              defaultRates: { ...config.defaultRates, zero: Number(e.target.value) }
            })}
            style={getInputStyle()}
            disabled
          />
        </div>
      </div>
    </div>
  )

  return (
    <div className={className} style={getOverlayStyle()}>
      <div style={getModalStyle()}>
        <div style={getHeaderStyle()}>
          <h2 style={getTitleStyle()}>VAT Setup</h2>
          <p style={getSubtitleStyle()}>Step {step} of 3</p>
        </div>

        <div style={getStepIndicatorStyle()}>
          {[1, 2, 3].map((stepNumber) => (
            <div
              key={stepNumber}
              style={getStepDotStyle(
                stepNumber === step,
                stepNumber < step
              )}
            />
          ))}
        </div>

        {step === 1 && renderStep1()}
        {step === 2 && renderStep2()}
        {step === 3 && renderStep3()}

        {/* Error Display */}
        {error && (
          <div style={{
            padding: '12px',
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '6px',
            marginBottom: '16px',
          }}>
            <p style={{
              color: '#dc2626',
              fontSize: '14px',
              margin: 0,
            }}>
              {error}
            </p>
          </div>
        )}

        <div style={getButtonGroupStyle()}>
          <button
            onClick={onClose}
            style={getButtonStyle('secondary')}
          >
            Cancel
          </button>
          {step > 1 && (
            <button
              onClick={handleBack}
              style={getButtonStyle('secondary')}
            >
              Back
            </button>
          )}
          <button
            onClick={handleNext}
            style={getButtonStyle('primary')}
            disabled={(step === 1 && !config.enabled) || loading}
          >
            {loading && step === 3 ? 'Saving...' : step === 3 ? 'Complete Setup' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}
