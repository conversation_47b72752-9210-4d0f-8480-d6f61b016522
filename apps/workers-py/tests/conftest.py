"""Pytest configuration and fixtures."""

import asyncio
import tempfile
from collections.abc import As<PERSON><PERSON>enerator, Generator
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import ASGITransport, AsyncClient

from workers_py.config import Settings
from workers_py.main import app
from workers_py.extraction.models import (
    BoundingBox,
    DocumentContext,
    DocumentPage,
    OcrLine,
    OcrResult,
    OcrToken,
)
from workers_py.models import (
    ExtractionResult,
    SupplierInfo,
    CustomerInfo,
    InvoiceInfo,
    TotalsInfo,
    PaymentInstructions,
    LineItem,
    VatRate,
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """Test settings fixture."""
    return Settings(
        DEBUG=True,
        LOG_LEVEL="debug",
        DATABASE_URL="sqlite:///test.db",
        REDIS_URL="redis://localhost:6379/15",  # Use test database
        CELERY_BROKER_URL="redis://localhost:6379/15",
        CELERY_RESULT_BACKEND="redis://localhost:6379/15",
        ENVIRONMENT="testing",
    )


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    """Test client fixture."""
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Async test client fixture."""
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_database():
    """Mock database fixture."""
    mock_db = AsyncMock()
    mock_db.execute = AsyncMock()
    mock_db.fetch = AsyncMock()
    mock_db.fetchrow = AsyncMock()
    return mock_db


@pytest.fixture
def temp_pdf_file():
    """Create a temporary PDF file for testing."""
    # This would create a simple test PDF
    # For now, we'll create a text file with PDF extension
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
        tmp.write(b"%PDF-1.4\n%Fake PDF for testing\nSample text content")
        tmp_path = Path(tmp.name)

    yield tmp_path

    # Cleanup
    tmp_path.unlink(missing_ok=True)


@pytest.fixture
def temp_image_file():
    """Create a temporary image file for testing."""
    from PIL import Image

    # Create a simple test image
    image = Image.new("RGB", (100, 100), color="white")

    with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
        image.save(tmp.name)
        tmp_path = Path(tmp.name)

    yield tmp_path

    # Cleanup
    tmp_path.unlink(missing_ok=True)


@pytest.fixture
def sample_document_text():
    """Sample document text for testing."""
    return """
    INVOICE #12345

    Date: 2023-12-01
    Due Date: 2023-12-31

    Bill To:
    Acme Corporation
    123 Main Street
    Anytown, ST 12345

    Description: Professional Services
    Amount: $1,234.56
    Tax: $123.46
    Total: $1,358.02

    Please remit payment by the due date.

    Thank you for your business!
    """


@pytest.fixture
def mock_celery_task():
    """Mock Celery task fixture."""
    mock_task = MagicMock()
    mock_task.id = "test-task-123"
    mock_task.state = "SUCCESS"
    mock_task.result = {"status": "completed", "message": "Task completed successfully"}
    mock_task.delay = MagicMock(return_value=mock_task)
    return mock_task


@pytest.fixture
def mock_ocr_service():
    """Mock OCR service fixture."""
    mock_service = MagicMock()
    mock_service.extract_text_from_image.return_value = {
        "text": "Sample OCR text",
        "metadata": {
            "ocr_engine": "tesseract",
            "average_confidence": 95.5,
            "character_count": 15,
            "word_count": 3,
        },
        "words": [],
    }
    mock_service.extract_text_from_pdf.return_value = {
        "text": "Sample PDF OCR text",
        "metadata": {
            "ocr_engine": "tesseract",
            "pages_processed": 1,
            "average_confidence": 90.0,
            "character_count": 19,
            "word_count": 4,
        },
        "pages": [],
        "words": [],
    }
    return mock_service


@pytest.fixture
def mock_pdf_service():
    """Mock PDF service fixture."""
    mock_service = MagicMock()
    mock_service.extract_text_and_metadata.return_value = {
        "text": "Sample PDF text content",
        "metadata": {
            "extractor": "pdfplumber",
            "page_count": 1,
            "pages_processed": 1,
            "has_text": True,
            "character_count": 23,
            "word_count": 4,
            "table_count": 0,
            "image_count": 0,
        },
        "pages": [],
        "tables": [],
        "images": [],
    }
    mock_service.is_text_based_pdf.return_value = True
    return mock_service


@pytest.fixture
def mock_ai_service():
    """Mock AI service fixture."""
    mock_service = MagicMock()
    mock_service.analyze_document.return_value = {
        "document_type": "invoice",
        "confidence": 0.95,
        "categories": ["financial", "business"],
        "entities": {
            "invoice_number": ["12345"],
            "amount": ["$1,234.56"],
            "date": ["2023-12-01"],
        },
        "summary": "Invoice #12345 for professional services totaling $1,234.56",
        "key_information": {"total_amount": "1234.56", "invoice_number": "12345"},
        "language": "en",
        "sentiment": {"sentiment": "neutral", "confidence": 0.5},
    }
    return mock_service


def _build_document_page() -> DocumentPage:
    """Create a lightweight DocumentPage for OCR fixtures."""
    from PIL import Image

    image = Image.new("RGB", (400, 600), color="white")
    return DocumentPage.from_image(1, image)


@pytest.fixture
def sample_document_context() -> DocumentContext:
    """Representative document context covering supplier/totals/payment cues."""
    page = _build_document_page()

    line_specs = [
        ("p1_l1", "Test Company BVBA", BoundingBox(0.05, 0.05, 0.55, 0.10)),
        ("p1_l2", "VAT BE0123456789", BoundingBox(0.05, 0.11, 0.45, 0.16)),
        ("p1_l3", "Total 121.00 EUR", BoundingBox(0.05, 0.18, 0.42, 0.23)),
        ("p1_l4", "IBAN ****************", BoundingBox(0.05, 0.25, 0.60, 0.30)),
    ]

    tokens: list[OcrToken] = []
    lines: list[OcrLine] = []
    for idx, (line_id, text, bbox) in enumerate(line_specs, start=1):
        token_id = f"p1_t{idx}"
        tokens.append(
            OcrToken(
                id=token_id,
                text=text,
                bbox=bbox,
                confidence=0.95,
                page=1,
                line_id=line_id,
            )
        )
        lines.append(
            OcrLine(
                id=line_id,
                text=text,
                bbox=bbox,
                page=1,
                token_ids=[token_id],
            )
        )

    text = "\n".join(line.text for line in lines)
    ocr_result = OcrResult(text=text, tokens=tokens, lines=lines, pages=[page])
    return DocumentContext(ocr=ocr_result, mime_type="application/pdf")


@pytest.fixture
def empty_document_context() -> DocumentContext:
    """Document context without textual content to trigger validation errors."""
    page = _build_document_page()
    ocr_result = OcrResult(text="", tokens=[], lines=[], pages=[page])
    return DocumentContext(ocr=ocr_result, mime_type="application/pdf")


@pytest.fixture
def mock_extraction_result() -> ExtractionResult:
    """Provide a reusable ExtractionResult aligned with the new schema."""
    return ExtractionResult(
        supplier=SupplierInfo(
            name="Test Company BVBA",
            vat="BE0123456789",
            address="Main Street 1, 1000 Brussels",
        ),
        customer=CustomerInfo(name="Client NV", vat="BE1111111111", address="Examplelaan 5"),
        invoice=InvoiceInfo(
            number="INV-2024-001",
            issue_date="2024-08-15",
            due_date="2024-09-15",
            currency="EUR",
            net="100.00",
            vat="21.00",
            gross="121.00",
        ),
        totals=TotalsInfo(net="100.00", vat="21.00", gross="121.00", currency="EUR"),
        payment_instructions=PaymentInstructions(
            iban="****************",
            bic="BBRUBEBB",
            structured_ref="+++123/4567/89012+++",
        ),
        lines=[
            LineItem(
                description="Consulting services",
                quantity="1",
                unit_price="100.00",
                vat_rate=VatRate.STANDARD,
            )
        ],
        confidence=0.9,
    )


@pytest.fixture
def sample_request_data():
    """Sample request data for testing."""
    return {
        "entity_id": "test-entity-123",
        "file_url": "https://example.com/test-document.pdf",
        "options": {
            "ocr_language": "eng",
            "extract_tables": True,
            "ai_categorize": True,
            "confidence_threshold": 0.8,
        },
    }


# Async fixtures
@pytest_asyncio.fixture
async def mock_async_database():
    """Mock async database fixture."""
    mock_db = AsyncMock()

    # Mock connection context manager
    async def mock_get_database():
        return mock_db

    return mock_get_database


# Integration test fixtures
@pytest.fixture(scope="session")
def docker_compose_file():
    """Docker compose file for integration tests."""
    return Path(__file__).parent.parent / "docker-compose.yml"


@pytest.fixture(scope="session")
def docker_services():
    """Start docker services for integration tests."""
    # This would be used with pytest-docker for full integration tests
    # For now, we'll skip this and use mocks
    pass
