# Claude AI Assistant Guide for Ledgerly/BelBooks

This document contains specific commands and procedures for Claude AI assistants working on the Ledgerly/BelBooks project.

## Document Processing Reset Commands

When the user asks to "reset documents", "reset all documents", "clean documents", or similar requests, use these commands:

### Quick Reset Commands
- **Reset staging documents** (UI database): `npm run reset-docs:staging`
- **Reset local documents**: `npm run reset-docs`
- **Reset production documents**: `npm run reset-docs:prod` (requires confirmation)

### What These Commands Do
1. **Reset inbox documents** - Sets all documents back to `'uploaded'` status (unprocessed)
2. **Clear extracted data** - Removes all AI-extracted invoices, journals, and journal lines
3. **Clean processing results** - Clears document processing results and task status
4. **Remove cache** - Clears file processing cache and embeddings
5. **Preserve originals** - Keeps original document files and basic metadata intact

### When to Use
- User wants to test document processing from scratch
- Documents are stuck in failed/processing state
- Need clean slate for demo or testing
- After making changes to AI extraction logic
- Before testing new document processing features

### Safety Notes
- Scripts are idempotent (safe to run multiple times)
- Original document files are preserved
- Only processing status and extracted data are reset
- Production reset requires explicit user confirmation

## Development Environment Commands

### Start/Stop Services
- **Start all services**: `./dev-setup.sh`
- **Check service status**: `./dev-setup.sh --check`
- **Stop all services**: `./dev-setup.sh --stop`

### Service URLs
- **Frontend (UI)**: http://localhost:3000
- **BFF API**: http://localhost:4000
- **Workers API**: http://localhost:8000
- **Supabase Studio**: http://localhost:54323

### Database Commands
- **Apply local migrations**: `pnpm db:migrate:local`
- **Apply staging migrations**: `pnpm db:migrate:staging`
- **Generate types from local**: `pnpm db:types`
- **Generate types from staging**: `pnpm db:types:staging`

## Testing Commands

### JavaScript/TypeScript
- **Run all tests**: `pnpm test`
- **Watch mode**: `pnpm test:watch`
- **Coverage**: `pnpm test:coverage`
- **Lint**: `pnpm lint`
- **Type check**: `pnpm typecheck`

### Python (Workers)
- **Run tests**: `cd apps/workers-py && uv run pytest`
- **Lint**: `cd apps/workers-py && uv run ruff check`
- **Format check**: `cd apps/workers-py && uv run black --check`
- **Type check**: `cd apps/workers-py && uv run mypy`

## Common User Requests and Responses

### "Reset all documents" / "Clean documents" / "Reset processing"
**Response**: I'll reset all documents to unprocessed state and clear extracted data.
**Action**: Run `npm run reset-docs:staging` (for UI testing) or `npm run reset-docs` (for local)

### "Start the servers" / "Run the development environment"
**Response**: I'll start all development services.
**Action**: Run `./dev-setup.sh`

### "Check if services are running"
**Response**: I'll check the status of all services.
**Action**: Run `./dev-setup.sh --check`

### "Test document processing" / "Upload documents for testing"
**Response**: The servers are running and documents are reset. You can now:
1. Open http://localhost:3000
2. Upload documents through the UI
3. Test the AI extraction pipeline

## Environment Information

### Database Connections
- **Local**: localhost:54322 (Supabase local)
- **Staging**: Uses STAGING_DATABASE_URL from .env.local (UI connects here)
- **Production**: Uses PRODUCTION_DATABASE_URL from .env.local

### Key Environment Variables
- `DATABASE_URL` - Local/staging database connection
- `STAGING_DATABASE_URL` - Staging database connection
- `PRODUCTION_DATABASE_URL` - Production database connection
- `WORKERS_URL` - Workers service URL (http://localhost:8000)
- `REDIS_URL` - Redis connection (redis://localhost:6379)

## Troubleshooting

### Common Issues
1. **Services won't start**: Check if Docker is running, ports are free
2. **Database connection failed**: Verify Supabase is running, check connection strings
3. **Reset script fails**: Ensure database is accessible, check environment variables
4. **Workers not responding**: Check if Python dependencies are installed (`uv sync`)

### Quick Fixes
- **Kill stuck processes**: `./dev-setup.sh --stop` then restart
- **Clear logs**: Check `.dev-logs/` directory for service logs
- **Reset environment**: Stop services, reset documents, restart services

## Memory Triggers

Remember these key phrases and their corresponding actions:
- "reset documents" → `npm run reset-docs:staging`
- "start servers" → `./dev-setup.sh`
- "check status" → `./dev-setup.sh --check`
- "test processing" → Reset docs + start servers + guide to UI
- "clean slate" → Reset docs + clear any stuck processes
- "demo prep" → Reset staging docs + verify services running
