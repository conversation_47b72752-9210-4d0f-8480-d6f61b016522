import { useState, useEffect, useCallback } from 'react'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'

// Database row type
interface InboxDocumentRow {
  id: number
  entity_id: number
  path?: string | null
  mime_type?: string | null
  status: string
  created_at: string
  updated_at: string
  supplier_name?: string | null
  invoice_number?: string | null
  invoice_date?: string | null
  gross_amount?: string | null
  vat_amount?: string | null
  net_amount?: string | null
  [key: string]: any  // For any additional fields
}

// Application type
interface InboxDocument {
  id: number
  entity_id: number
  path: string
  mime_type: string
  status: string  // Using string for flexibility
  created_at: string
  updated_at: string
  supplier_name?: string | null
  invoice_number?: string | null
  invoice_date?: string | null
  gross_amount?: string | null
  vat_amount?: string | null
  net_amount?: string | null
}

interface Pagination {
  page: number
  per_page: number
  total: number
  total_pages: number
}

interface UseInboxDocumentsOptions {
  status?: string
  page?: number
  limit?: number
}

interface UseInboxDocumentsReturn {
  documents: InboxDocument[]
  pagination: Pagination | null
  loading: boolean
  error: string | null
  refreshDocuments: () => Promise<void>
}

export function useInboxDocuments(options?: UseInboxDocumentsOptions): UseInboxDocumentsReturn {
  const [documents, setDocuments] = useState<InboxDocument[]>([])
  const [pagination, setPagination] = useState<Pagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { currentEntity, isValid } = useOrgEntitySelection()
  const supabase = createSecureBrowserClient()

  const fetchDocuments = useCallback(async () => {
    if (!isValid || !currentEntity) {
      setDocuments([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const limit = options?.limit || 50
      const page = options?.page || 1
      const offset = (page - 1) * limit

      // Build query
      let query = supabase
        .from('inbox_documents')
        .select('*', { count: 'exact' })
        .eq('entity_id', currentEntity.entity_id!)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      // Add status filter if provided
      if (options?.status) {
        query = query.eq('status', options.status)
      }

      // Fetch documents from the inbox_documents table
      const { data, error: fetchError, count } = await query as {
        data: InboxDocumentRow[] | null
        error: any
        count: number | null
      }

      if (fetchError) {
        console.error('Error fetching inbox documents:', fetchError)
        setError(fetchError.message)
        setDocuments([])
        return
      }

      // Map the data to ensure all required fields are present
      const mappedDocuments: InboxDocument[] = (data || []).map((doc: InboxDocumentRow) => ({
        id: doc.id,
        entity_id: doc.entity_id,
        path: doc.path || '',
        mime_type: doc.mime_type || 'application/octet-stream',
        status: doc.status || 'uploaded',
        created_at: doc.created_at,
        updated_at: doc.updated_at,
        supplier_name: doc.supplier_name || null,
        invoice_number: doc.invoice_number || null,
        invoice_date: doc.invoice_date || null,
        gross_amount: doc.gross_amount || null,
        vat_amount: doc.vat_amount || null,
        net_amount: doc.net_amount || null,
      }))

      setDocuments(mappedDocuments)

      // Set pagination info
      if (count !== null) {
        const limit = options?.limit || 50
        const page = options?.page || 1
        setPagination({
          page,
          per_page: limit,
          total: count,
          total_pages: Math.ceil(count / limit)
        })
      }
    } catch (err) {
      console.error('Error in useInboxDocuments:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch documents')
      setDocuments([])
    } finally {
      setLoading(false)
    }
  }, [currentEntity, isValid, supabase, options])

  useEffect(() => {
    void fetchDocuments()
  }, [fetchDocuments])

  return {
    documents,
    pagination,
    loading,
    error,
    refreshDocuments: fetchDocuments
  }
}