# AI Extraction Implementation Guide

## Overview

This document describes the implementation of the Track B AI extraction feature using Google's Gemini model via the LangExtract library. The implementation follows the existing adapter pattern and includes shadow mode for safe comparison between stub and AI providers.

## Architecture

### Provider Pattern
The system uses an adapter pattern that allows switching between different extraction providers:

```
ExtractionAdapter (ABC)
├── StubExtractionAdapter (deterministic fake data)  
├── GeminiLangExtractAdapter (Gemini + LangExtract)
└── [Future: AnthropicAdapter, OpenAIAdapter, etc.]
```

### Shadow Mode
Shadow mode runs both the primary provider (e.g., Gemini) and a shadow provider (typically stub) in parallel, storing both results for comparison without affecting the primary workflow.

## Implementation Components

### 1. Core Adapters (`src/workers_py/adapters/`)

#### `base.py`
- Abstract `ExtractionAdapter` interface
- Returns `(ExtractionResult, confidence, metadata)` tuple
- Common `ExtractionError` exception class

#### `gemini_langextract.py` 
- Implements Gemini-powered extraction via LangExtract
- Handles Belgian invoice-specific prompts and examples
- Includes confidence scoring based on:
  - Field coverage (40% weight)
  - Grounding quality (40% weight) 
  - Arithmetic consistency (20% weight)

#### `stub.py`
- Compatible version of the original stub adapter
- Generates deterministic data based on text hash
- Maintains consistent interface with real adapters

### 2. Enhanced Services (`src/workers_py/services/`)

#### `extract_v2.py`
- `EnhancedExtractionService` with provider switching
- Shadow mode support for A/B comparison
- Retry logic with exponential backoff
- Supplier template enhancement
- Confidence-based auto-suggestion thresholds

#### `text_extraction.py`
- `TextExtractionService` for document-to-text conversion
- Supports PDF (via pdfminer.six) and images (via Tesseract OCR)
- MIME type detection using `python-magic`
- Graceful fallback from PDF text to OCR

### 3. Validation (`src/workers_py/validation.py`)

#### `ExtractionValidator`
- Comprehensive validation of extraction results
- Belgian invoice-specific business rules
- Confidence adjustment based on validation results
- Field coverage analysis and arithmetic consistency checks

### 4. Enhanced Tasks (`src/workers_py/tasks_enhanced.py`)

#### `process_inbox_document_enhanced_task`
- Celery task using new extraction architecture
- Progress tracking through multiple stages
- Database updates with shadow mode data
- Auto-triggering of suggestion generation

### 5. Database Schema (`packages/db/migrations/0008_ai_extraction.sql`)

New columns added to `inbox_documents`:
- `extraction_alt` JSONB - Shadow extraction results
- `confidence_alt` NUMERIC - Shadow confidence score  
- `metadata` JSONB - Processing metadata and debugging info
- `text_length` INTEGER - Document text length
- `processing_time` NUMERIC - Extraction processing time
- `extraction_provider` TEXT - Provider used (stub/gemini_langextract)

New database functions:
- `is_ai_extraction_enabled(entity_id)` - Check feature flag
- `get_extraction_stats()` - Performance monitoring

## Configuration

### Environment Variables

```bash
# Provider selection
EXTRACTION_PROVIDER=gemini_langextract  # stub|gemini_langextract
GEMINI_MODEL=gemini-2.5-flash          # gemini-2.5-flash|gemini-2.5-pro  
AI_EXTRACT_RUN_MODE=shadow             # shadow|replace|disabled

# Google AI Studio authentication
GEMINI_API_KEY=your-gemini-api-key-here

# OR Vertex AI authentication
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_LOCATION=europe-west1
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/sa.json

# Processing settings
AI_EXTRACTION_TIMEOUT=20      # seconds
AI_EXTRACTION_RETRIES=2       # retry attempts
AI_CONFIDENCE_THRESHOLD=0.7   # auto-suggestion threshold
```

### Feature Flags

Per-entity feature flag `AIExtractGeminiEnabled` controls whether Gemini extraction is available for specific entities.

## Usage Patterns

### Shadow Mode (Recommended for rollout)
```python
# Environment: AI_EXTRACT_RUN_MODE=shadow
service = create_extraction_service(entity_id=123)
result = await service.process_document(text, entity_id)

# Access primary result (Gemini)
primary = result.primary_result
primary_confidence = result.primary_confidence  

# Access shadow result (stub) for comparison
shadow = result.shadow_result  
shadow_confidence = result.shadow_confidence
```

### Replace Mode (Production)
```python
# Environment: AI_EXTRACT_RUN_MODE=replace  
service = create_extraction_service(entity_id=123)
result = await service.process_document(text, entity_id)

# Only primary result available
extraction = result.primary_result
confidence = result.primary_confidence
```

## Monitoring & Debugging

### Extraction Statistics
```sql
-- Get performance stats by provider
SELECT * FROM get_extraction_stats(entity_id);

-- Compare shadow vs primary results  
SELECT * FROM v_extraction_comparison 
WHERE entity_id = 123 
ORDER BY created_at DESC;
```

### LangExtract Visualization
The system can generate HTML visualizations of extractions for debugging:

```python
metadata = extraction_result.metadata
if 'viz_html' in metadata:
    # Save or serve the HTML for visual debugging
    visualization_html = metadata['viz_html']
```

## Testing

### Unit Tests
- `test_gemini_adapter.py` - Adapter functionality
- `test_validation.py` - Validation logic  
- `test_extraction_service.py` - Service orchestration

### Integration Tests
Run tests with mock LangExtract results:
```bash
cd apps/workers-py
uv run pytest tests/ -v
```

### Golden Dataset Tests  
Create frozen test cases with known invoice texts and expected extractions for regression testing.

## Deployment Strategy

### Phase 1: Shadow Mode
1. Deploy with `AI_EXTRACT_RUN_MODE=shadow`
2. Enable `AIExtractGeminiEnabled` for pilot entities
3. Monitor extraction accuracy and performance
4. Compare Gemini vs stub results

### Phase 2: Replace Mode  
1. When accuracy ≥95% on critical fields, switch to `replace` mode
2. Gradually roll out to more entities via feature flags
3. Monitor error rates and API costs
4. Keep stub adapter as fallback

### Phase 3: Production Scale
1. Optimize prompts and examples based on real data
2. Consider Gemini Pro model for complex documents  
3. Implement cost monitoring and rate limiting
4. Add support for additional providers (OpenAI, Claude, etc.)

## Error Handling

### Graceful Degradation
- Gemini failures fall back to stub adapter
- Shadow extraction failures don't affect primary workflow
- Timeout and retry logic prevents hanging tasks

### Validation Integration
- Extraction results are validated before storage
- Low confidence extractions skip auto-suggestion
- Business rule violations are flagged

### Monitoring Integration
- Failed extractions logged with context
- Performance metrics tracked per provider
- Confidence distribution monitoring

## Cost Optimization

### Model Selection
- **gemini-2.5-flash**: Fast and cost-effective for most invoices
- **gemini-2.5-pro**: Higher accuracy for complex/handwritten documents

### Request Optimization
- Text preprocessing to remove unnecessary content
- Structured prompts to minimize token usage
- Caching and deduplication where appropriate

### Usage Monitoring
- Track API costs per entity and document type
- Set up alerting for unusual usage patterns
- Consider quota management for high-volume entities

## Security & Privacy

### Data Protection
- Invoice text is not logged in full (only hashes and metrics)
- API responses are not cached permanently  
- PII masking in debug outputs

### Access Control
- Feature flags control per-entity access
- RLS policies protect extraction data
- Service account permissions for API access

### Compliance
- GDPR-compliant data processing
- Audit trails for AI extraction usage
- Data retention policies for extraction results
