import { test, describe, expect, beforeEach, afterEach } from 'vitest'
import { FastifyInstance } from 'fastify'
import { buildApp } from '../src/index'

// Guard tests with environment variable to avoid running in CI without Supabase
const runTests = process.env.RUN_ROUTE_TESTS === '1'

const describeIf = runTests ? describe : describe.skip

// Helper to get authentication headers
const getAuthHeaders = () => ({
  'x-internal-key': process.env.INTERNAL_KEY || 'staging-secure-internal-key-change-in-production'
})

// Mock Supabase client and responses
const mockDocument = {
  id: 1,
  entity_id: 100,
  status: 'extracted',
  extraction: {
    supplier: { name: 'Test Supplier', vat: 'BE0123456789', address: 'Main Street 1, 1000 Brussels' },
    customer: null,
    invoice: { number: 'INV001', issue_date: '2024-01-01', due_date: '2024-01-31', currency: 'EUR' },
    totals: { net: '100.00', vat: '21.00', gross: '121.00', currency: 'EUR' },
    payment_instructions: { iban: '****************', bic: 'BBRUBEBB', structured_ref: '+++123/4567/89012+++' },
    lines: [{ description: 'Test Service', quantity: '1', unit_price: '100.00', vat_rate: 21, account_hint: 1001 }],
    confidence: 0.92,
    confidence_breakdown: { document: 0.92, fields: { 'supplier.name': 0.95 } },
    regions: [],
    candidates: {},
    page_previews: []
  },
  suggestion: {
    journalDate: '2024-01-01',
    reference: 'INV001',
    description: 'Test Supplier - Invoice INV001',
    lines: [
      { accountId: 1001, debit: '100.00', credit: '0.00', vatCodeId: 1, memo: 'Test Service' },
      { accountId: 1002, debit: '21.00', credit: '0.00', vatCodeId: 1, memo: 'VAT 21% on Test Service' },
      { accountId: 2001, debit: '0.00', credit: '121.00', memo: 'Test Supplier - Invoice INV001' }
    ]
  }
}

const mockOperatingMode = { mode: 'ledger', config: {} }

describeIf('Confirm Orchestrator Route Tests', () => {
  let app: FastifyInstance

  beforeEach(async () => {
    app = await buildApp()
  })

  afterEach(async () => {
    await app.close()
  })

  describe('Authentication & Authorization', () => {
    test('should return 401 when no JWT token provided', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: getAuthHeaders()
      })

      expect(response.statusCode).toBe(401)
      expect(response.json()).toEqual({
        success: false,
        error: 'Authentication required'
      })
    })

    test('should return 403 when user has no access to document', async () => {
      // Mock Supabase client that returns no document (RLS blocks access)
      const mockSupabaseClient = {
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: null, error: { message: 'Row not found' } })
            })
          })
        }),
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        }
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/999/confirm',
        payload: { document_id: 999 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore - Mock the userSupabase for this test
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([401, 403]).toContain(response.statusCode)
      if (response.statusCode === 403) {
        expect(response.json()).toEqual({
          success: false,
          error: 'Document not found or access denied'
        })
      }
    })
  })

  describe('Concurrency Control', () => {
    test('should return 409 when advisory lock cannot be acquired', async () => {
      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: false } // Lock failed
          return { data: null, error: null }
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: mockDocument, error: null })
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([401, 409]).toContain(response.statusCode)
      if (response.statusCode === 409) {
        expect(response.json()).toEqual({
          success: false,
          error: 'Document is currently being processed by another request'
        })
      }
    })
  })

  describe('Idempotency', () => {
    test('should return existing result when document already posted', async () => {
      const alreadyPostedDoc = {
        ...mockDocument,
        status: 'posted',
        posted_journal_id: 123,
        confirmed_at: '2024-01-01T10:00:00Z',
        confirmed_by: 'user-123'
      }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: alreadyPostedDoc, error: null })
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([200, 401]).toContain(response.statusCode)
      if (response.statusCode === 200) {
        const result = response.json()
        expect(result.success).toBe(true)
        expect(result.data.document_id).toBe(1)
        expect(result.data.status).toBe('posted')
        expect(result.data.journal_id).toBe(123)
        expect(result.data.idempotent).toBe(true)
      }
    })

    test('should return existing result when document already exported', async () => {
      const alreadyExportedDoc = {
        ...mockDocument,
        status: 'exported',
        export_ref: 'export_123456_1_abc',
        confirmed_at: '2024-01-01T10:00:00Z',
        confirmed_by: 'user-123'
      }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: alreadyExportedDoc, error: null })
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([200, 401]).toContain(response.statusCode)
      if (response.statusCode === 200) {
        const result = response.json()
        expect(result.success).toBe(true)
        expect(result.data.status).toBe('exported')
        expect(result.data.export_ref).toBe('export_123456_1_abc')
        expect(result.data.idempotent).toBe(true)
      }
    })
  })

  describe('State Validation', () => {
    test('should reject confirmation of uploaded document', async () => {
      const uploadedDoc = { ...mockDocument, status: 'uploaded' }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: uploadedDoc, error: null })
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([400, 401]).toContain(response.statusCode)
      if (response.statusCode === 400) {
        expect(response.json()).toEqual({
          success: false,
          error: "Cannot confirm document in status 'uploaded'. Must be 'extracted' or 'suggested'."
        })
      }
    })

    test('should reject confirmation of failed document', async () => {
      const failedDoc = { ...mockDocument, status: 'failed', error_msg: 'AI extraction failed' }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: failedDoc, error: null })
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabaseClient: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([400, 401]).toContain(response.statusCode)
      if (response.statusCode === 400) {
        expect(response.json()).toEqual({
          success: false,
          error: "Cannot confirm document in status 'failed'. Must be 'extracted' or 'suggested'."
        })
      }
    })
  })

  describe('Balance Validation', () => {
    test('should reject unbalanced journal suggestion', async () => {
      const unbalancedDoc = {
        ...mockDocument,
        status: 'suggested',
        suggestion: {
          journalDate: '2024-01-01',
          reference: 'INV001',
          description: 'Test',
          lines: [
            { accountId: 1001, debit: '100.00', credit: '0.00' },
            { accountId: 2001, debit: '0.00', credit: '120.00' } // Off by 1.00
          ]
        }
      }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: async () => ({ data: unbalancedDoc, error: null })
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([401, 422, 500]).toContain(response.statusCode)
      if (response.statusCode === 422) {
        expect(response.json().success).toBe(false)
        expect(response.json().error).toContain('Journal is not balanced')
      }
    })
  })

  describe('Ledger Mode Processing', () => {
    test('should successfully post journal in ledger mode', async () => {
      let updateCalled = false
      let auditCalled = false

      const mockSupabaseClient = {
        rpc: async (name: string, params?: any) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          if (name === 'rpc_post_journal') {
            expect(params.p_entity).toBe(100)
            expect(params.p_type).toBe('purchase')
            expect(params.p_description).toBe(mockDocument.suggestion.description)
            return { data: 456, error: null } // Return journal ID
          }
          return { data: null, error: null }
        },
        from: (table: string) => ({
          select: () => ({
            eq: () => ({
              single: async () => {
                if (table === 'inbox_documents') return { data: mockDocument, error: null }
                if (table === 'operating_modes') return { data: mockOperatingMode, error: null }
                return { data: null, error: null }
              }
            })
          }),
          insert: async () => {
            auditCalled = true
            return { data: null, error: null }
          },
          update: () => ({
            eq: () => {
              updateCalled = true
              return Promise.resolve({ data: null, error: null })
            }
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([200, 401]).toContain(response.statusCode)
      if (response.statusCode === 200) {
        const result = response.json()
        expect(result.success).toBe(true)
        expect(result.data.status).toBe('posted')
        expect(result.data.journal_id).toBe(456)
        expect(updateCalled).toBe(true)
        expect(auditCalled).toBe(true)
      }
    })

    test('should handle journal posting failure gracefully', async () => {
      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          if (name === 'rpc_post_journal') {
            return { data: null, error: { message: 'Account not found' } }
          }
          return { data: null, error: null }
        },
        from: (table: string) => ({
          select: () => ({
            eq: () => ({
              single: async () => {
                if (table === 'inbox_documents') return { data: mockDocument, error: null }
                if (table === 'operating_modes') return { data: mockOperatingMode, error: null }
                return { data: null, error: null }
              }
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([401, 500]).toContain(response.statusCode)
      if (response.statusCode === 500) {
        expect(response.json()).toEqual({
          success: false,
          error: 'Failed to post journal entry: Journal posting failed: Account not found'
        })
      }
    })
  })

  describe('Assist Mode Processing', () => {
    test('should successfully create export event in assist mode', async () => {
      let domainEventCreated = false
      let updateCalled = false

      const assistMode = { mode: 'assist', config: {} }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: (table: string) => ({
          select: () => ({
            eq: () => ({
              single: async () => {
                if (table === 'inbox_documents') return { data: mockDocument, error: null }
                if (table === 'operating_modes') return { data: assistMode, error: null }
                if (table === 'accounts') return { data: [], error: null }
                return { data: null, error: null }
              }
            }),
            in: () => ({
              single: async () => ({ data: [], error: null })
            })
          }),
          insert: async (data: any) => {
            if (table === 'domain_events') {
              expect(data.event_type).toBe('export_requested')
              expect(data.entity_id).toBe(100)
              domainEventCreated = true
            }
            return { data: null, error: null }
          },
          update: () => ({
            eq: () => {
              updateCalled = true
              return Promise.resolve({ data: null, error: null })
            }
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { document_id: 1 },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([200, 401]).toContain(response.statusCode)
      if (response.statusCode === 200) {
        const result = response.json()
        expect(result.success).toBe(true)
        expect(result.data.status).toBe('exported')
        expect(result.data.export_ref).toMatch(/^export_\d+_1_[a-z0-9]+$/)
        expect(domainEventCreated).toBe(true)
        expect(updateCalled).toBe(true)
      }
    })
  })

  describe('Correction Handling', () => {
    test('should handle correction to extraction', async () => {
      const correctedExtraction = {
        ...mockDocument.extraction,
        totals: { ...mockDocument.extraction.totals, gross: '130.00' } // User corrected amount
      }

      let suggestionRecalculated = false

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          if (name === 'rpc_post_journal') return { data: 789, error: null }
          return { data: null, error: null }
        },
        from: (table: string) => ({
          select: () => ({
            eq: () => ({
              single: async () => {
                if (table === 'inbox_documents') return { data: mockDocument, error: null }
                if (table === 'operating_modes') return { data: mockOperatingMode, error: null }
                if (table === 'accounts') return { data: [
                  { id: 1001, code: '6000', name: 'Expenses', account_type: 'expense' },
                  { id: 2001, code: '4400', name: 'Accounts Payable', account_type: 'accounts_payable' }
                ], error: null }
                if (table === 'vat_codes') return { data: [
                  { id: 1, rate: 0.21, purchase_account_id: 1002 }
                ], error: null }
                return { data: null, error: null }
              }
            }),
            in: () => ({ 
              single: async () => ({ data: [], error: null }) 
            })
          }),
          insert: async () => ({ data: null, error: null }),
          update: () => ({
            eq: () => {
              suggestionRecalculated = true
              return Promise.resolve({ data: null, error: null })
            }
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { 
          document_id: 1,
          correction: correctedExtraction
        },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([200, 401]).toContain(response.statusCode)
      if (response.statusCode === 200) {
        expect(suggestionRecalculated).toBe(true)
      }
    })

    test('should reject correction that results in unbalanced journal', async () => {
      // This would be a malformed correction that can't generate balanced lines
      const badCorrection = {
        supplier: { name: 'Bad Supplier' },
        invoice: { number: 'BAD', issue_date: '2024-01-01', currency: 'EUR' },
        totals: { net: 'invalid', vat: 'invalid', gross: 'invalid', currency: 'EUR' },
        lines: []
      }

      const mockSupabaseClient = {
        rpc: async (name: string) => {
          if (name === 'pg_try_advisory_xact_lock') return { data: true }
          return { data: null, error: null }
        },
        from: (table: string) => ({
          select: () => ({
            eq: () => ({
              single: async () => {
                if (table === 'inbox_documents') return { data: mockDocument, error: null }
                return { data: null, error: null }
              }
            })
          })
        })
      }

      const response = await app.inject({
        method: 'POST',
        url: '/documents/1/confirm',
        payload: { 
          document_id: 1,
          correction: badCorrection
        },
        headers: {
          ...getAuthHeaders(),
          authorization: 'Bearer valid-jwt-token'
        },
        // @ts-ignore
        userSupabase: mockSupabaseClient,
        user: { id: 'user-123' }
      })

      expect([401, 422, 500]).toContain(response.statusCode)
      if (response.statusCode === 422) {
        expect(response.json().success).toBe(false)
        expect(response.json().error).toContain('Failed to generate suggestion from correction')
      }
    })
  })
})
