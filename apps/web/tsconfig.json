{"compilerOptions": {"lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@ledgerly/types": ["../../packages/types/src/index.ts"], "@ledgerly/types/*": ["../../packages/types/src/*"], "@ledgerly/dal": ["../../packages/dal/src/index.ts"], "@ledgerly/dal/*": ["../../packages/dal/src/*"], "@belbooks/types": ["../../packages/types/src/index.ts"], "@belbooks/types/*": ["../../packages/types/src/*"], "@belbooks/dal": ["../../packages/dal/src/index.ts"], "@belbooks/dal/*": ["../../packages/dal/src/*"], "@belbooks/import-service": ["../../packages/import-service/src/index.ts"], "@belbooks/import-service/*": ["../../packages/import-service/src/*"], "@belbooks/domain-invoicing": ["../../packages/domain-invoicing/src/index.ts"], "@belbooks/domain-invoicing/*": ["../../packages/domain-invoicing/src/*"]}, "types": ["vitest/globals"]}, "include": ["next-env.d.ts", "jest-dom.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "__tests__/security/middleware-integration.test.ts", "__tests__/security/session-security.test.ts"]}