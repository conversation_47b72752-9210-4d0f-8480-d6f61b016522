export type UISuggestionLine = {
  accountId: number
  debit?: string
  credit?: string
  vatCodeId?: number
  memo?: string
}

export type UISuggestion = {
  journalDate?: string
  reference?: string
  description?: string
  lines: UISuggestionLine[]
}

function getString(
  obj: Record<string, unknown>,
  ...keys: string[]
): string | undefined {
  for (const k of keys) {
    // Keys are controlled literals; safe indexing
    // eslint-disable-next-line security/detect-object-injection
    const v = obj[k]
    if (typeof v === 'string' && v.length > 0) return v
  }
  return undefined
}

function getNumber(
  obj: Record<string, unknown>,
  ...keys: string[]
): number | undefined {
  for (const k of keys) {
    // Keys are controlled literals; safe indexing
    // eslint-disable-next-line security/detect-object-injection
    const v = obj[k]
    if (typeof v === 'number') return v
    if (typeof v === 'string' && v.trim() !== '' && !Number.isNaN(Number(v)))
      return Number(v)
  }
  return undefined
}

export function normalizeSuggestion(input: unknown): UISuggestion {
  const base: UISuggestion = { lines: [] }
  if (!input || typeof input !== 'object') return base
  const obj = input as Record<string, unknown>

  base.journalDate = getString(obj, 'journalDate', 'journal_date')
  base.reference = getString(obj, 'reference')
  base.description = getString(obj, 'description')

  const rawLines = obj['lines']
  if (Array.isArray(rawLines)) {
    base.lines = rawLines.map(item => {
      const r =
        item && typeof item === 'object'
          ? (item as Record<string, unknown>)
          : {}
      return {
        accountId: getNumber(r, 'accountId', 'account_id') ?? 0,
        debit: getString(r, 'debit'),
        credit: getString(r, 'credit'),
        vatCodeId: getNumber(r, 'vatCodeId', 'vat_code_id'),
        memo: getString(r, 'memo'),
      }
    })
  }

  return base
}
