"""Deterministic table parser for OCR text to recover invoice line rows.

This is a heuristic parser intended as a fallback/assist for the LLM output.
It targets common Dutch/French/English headers and row formats typically seen on
Belgian supplier invoices.
"""

from __future__ import annotations

import re
from dataclasses import dataclass
import unicodedata
from typing import Iterable, List, Optional


DECIMAL_RE = r"(?:(?:\d{1,3}(?:[\.\s]\d{3})*|\d+)(?:[\.,]\d{1,2})?)"
PERCENT_RE = r"(?:\d{1,2})(?:[\.,]\d{0,2})?%?"


@dataclass
class ParsedRow:
    description: str
    quantity: Optional[str] = None
    unit_price: Optional[str] = None
    vat_rate: Optional[str] = None
    line_net: Optional[str] = None
    line_vat: Optional[str] = None
    line_gross: Optional[str] = None


HEADER_SYNONYMS = {
    # All values are normalized (lowercased, accents removed)
    "description": [
        "omschrijving", "beschrijving", "description", "omschrijving/description"
    ],
    "quantity": [
        "aantal", "qty", "qte", "qte.", "quantite", "quantity", "qté"
    ],
    "unit_price": [
        "prijs ex btw", "unit price", "pu ht", "price", "prijs", "prix unitaire",
        "pu", "unit", "prix ht", "excl vat", "ex btw", "ex vat"
    ],
    "vat": [
        "btw", "tva", "vat", "btw %", "vat %", "taux tva", "tax", "tax %"
    ],
    "line_net": [
        "bedrag ex btw", "ht", "ex btw", "subtotal ht", "sous-total ht", "line net",
        "amount ex vat", "net"
    ],
    "line_gross": [
        "bedrag incl btw", "ttc", "incl btw", "total ttc", "line gross", "gross",
        "amount incl vat", "incl vat"
    ],
}


def _norm(s: str) -> str:
    """Normalize string for robust header/row matching (lower + strip accents)."""
    s = unicodedata.normalize("NFKD", s).encode("ascii", "ignore").decode("ascii")
    return s.lower().strip()


def _clean_amount(s: str) -> str:
    s = s.strip().replace("€", "")
    s = s.replace(" ", "")
    s = s.replace(",", ".")
    return s


def parse_ocr_table_lines(text: str) -> List[ParsedRow]:
    """Parse OCR text and return a list of parsed line rows.

    The function is conservative: it only returns rows when it can recover a
    description and at least two numeric columns that look like quantities/prices.
    """
    lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
    rows: list[ParsedRow] = []

    # Identify a header block to increase confidence (optional)
    header_idx = -1
    # Build a lenient header regex from normalized synonyms
    header_terms = (
        HEADER_SYNONYMS["description"]
        + HEADER_SYNONYMS["quantity"]
        + HEADER_SYNONYMS["unit_price"]
        + HEADER_SYNONYMS["vat"]
        + HEADER_SYNONYMS["line_net"]
        + HEADER_SYNONYMS["line_gross"]
    )
    header_pattern = re.compile(
        r"|".join(re.escape(t) for t in set(_norm(t) for t in header_terms)), re.IGNORECASE
    )
    for i, ln in enumerate(lines[:20]):  # look at first 20 lines for headers
        if header_pattern.search(ln):
            header_idx = i
            break

    # Regex: capture description then a run of numeric tokens
    token_re = re.compile(rf"^(?P<desc>[^\d%€]*?)\s*((?P<num>(?:{DECIMAL_RE}|{PERCENT_RE}))(\s+|$).*)$")

    for i, ln in enumerate(lines):
        m = token_re.match(ln)
        if not m:
            continue
        desc = m.group("desc").strip("-–:; ")
        if not desc or len(desc) < 3:
            continue

        # Extract all numeric-like tokens
        nums = re.findall(rf"{DECIMAL_RE}|{PERCENT_RE}", ln)
        # Basic heuristic: need at least quantity, unit, and one of totals
        if len(nums) < 3:
            continue

        # Try to interpret tokens: prefer pattern [qty, unit, vat%, line_net, line_vat|line_gross]
        qty = None
        unit = None
        vat = None
        line_net = None
        line_vat = None
        line_gross = None

        # First token could be qty when integer-like
        first = nums[0].replace(",", ".")
        if re.fullmatch(r"\d+(?:\.0+)?", first):
            qty = _clean_amount(nums[0])
            if len(nums) >= 2:
                unit = _clean_amount(nums[1])
                # seek percent among remaining
                for t in nums[2:]:
                    if "%" in t:
                        vat = _clean_amount(t.replace("%", ""))
                        break
                # the last two often are net and vat/gross
                if len(nums) >= 4:
                    line_net = _clean_amount(nums[-2])
                    line_last = _clean_amount(nums[-1])
                    # If last looks like percent, swap
                    if re.fullmatch(r"\d+(?:\.\d+)?", line_last):
                        # Heuristic: treat last as gross, unless we saw vat% then it could be vat amount
                        line_gross = line_last
                    else:
                        line_gross = None
                if len(nums) >= 5:
                    # try to use third-from-last as VAT amount when present
                    candidate = _clean_amount(nums[-1])
                    # Since above we used last as gross heuristically, set VAT from difference
                    # but also try to pick explicit token with percent sign earlier
                    pass
        else:
            # If not starting with qty, skip (we avoid false positives)
            continue

        rows.append(
            ParsedRow(
                description=desc,
                quantity=qty,
                unit_price=unit,
                vat_rate=vat,
                line_net=line_net,
                line_vat=line_vat,
                line_gross=line_gross,
            )
        )

    return rows
