'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { SessionTimeoutIndicator } from '@/components/SessionTimeoutDialog'
import { OrgEntityPicker } from '@/components/OrgEntityPicker'

// Zen UI Navigation Items - Using minimal Feather-style SVG icons
const getNavigationItems = (isSME: boolean, isFirm: boolean) => [
  {
    name: 'Dashboard',
    href: '/dashboard' as const,
    icon: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
      >
        <rect x="3" y="3" width="7" height="7" />
        <rect x="14" y="3" width="7" height="7" />
        <rect x="14" y="14" width="7" height="7" />
        <rect x="3" y="14" width="7" height="7" />
      </svg>
    ),
  },
  {
    name: isFirm ? 'Client Documents' : 'Documents',
    href: '/inbox' as const,
    icon: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
      >
        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" />
      </svg>
    ),
  },
  {
    name: 'Ledger',
    href: '/ledger' as const,
    icon: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
      >
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
        <polyline points="14,2 14,8 20,8" />
        <line x1="16" y1="13" x2="8" y2="13" />
        <line x1="16" y1="17" x2="8" y2="17" />
        <polyline points="10,9 9,9 8,9" />
      </svg>
    ),
  },
  {
    name: 'VAT',
    href: '/vat' as const,
    icon: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
      >
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
        <polyline points="14,2 14,8 20,8" />
        <line x1="16" y1="13" x2="8" y2="13" />
        <line x1="16" y1="17" x2="8" y2="17" />
      </svg>
    ),
  },
  {
    name: 'Roles',
    href: '/roles' as const,
    icon: (
      <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
      >
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
        <circle cx="9" cy="7" r="4" />
        <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
        <path d="M16 3.13a4 4 0 0 1 0 7.75" />
      </svg>
    ),
  },
]

export function Navigation() {
  const pathname = usePathname()
  const router = useRouter()
  const { user, loading, signOut, sessionSecurity } = useAuth()
  const { isSME, isFirm } = useOrgEntitySelection()

  // Redirect to login if not authenticated (avoid router.replace during render)
  useEffect(() => {
    if (!loading && !user) {
      router.replace('/login')
    }
  }, [loading, user, router])

  // Don't show navigation on auth pages
  if (
    pathname === '/login' ||
    pathname === '/forgot-password' ||
    pathname === '/reset-password' ||
    pathname.startsWith('/auth/') ||
    pathname === '/onboarding'
  ) {
    return null
  }

  // Show loading state - Zen UI styled
  if (loading) {
    return (
      <nav
        className="flex flex-col h-full"
        style={{
          width: '240px',
          background: 'var(--zen-surface)',
          borderRight: '1px solid var(--zen-border)',
          padding: '24px 0',
        }}
      >
        <div className="animate-pulse px-6">
          <div
            className="rounded mb-2"
            style={{
              height: '32px',
              background: 'var(--zen-border)',
              marginBottom: '4px',
            }}
          ></div>
          <div
            className="rounded mb-8"
            style={{
              height: '16px',
              background: 'var(--zen-border)',
              marginBottom: '32px',
            }}
          ></div>
          <div className="space-y-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <div
                key={i}
                className="rounded"
                style={{
                  height: '40px',
                  background: 'var(--zen-border)',
                  margin: '2px 12px',
                }}
              ></div>
            ))}
          </div>
        </div>
      </nav>
    )
  }

  if (!user) return null

  const handleSignOut = async () => {
    try {
      await signOut()
      router.replace('/login')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  return (
    <nav
      className="flex flex-col h-full"
      style={{
        width: '240px',
        background: 'var(--zen-surface)',
        borderRight: '1px solid var(--zen-border)',
        padding: '24px 0',
      }}
    >
      {/* Header - Simple wordmark */}
      <div style={{ padding: '0 24px', marginBottom: '32px' }}>
        <h1
          style={{
            fontSize: '20px',
            fontWeight: 600,
            color: 'var(--zen-primary-text)',
            margin: '0 0 4px 0',
            fontFamily:
              "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
          }}
        >
          BelBooks
        </h1>
        <p
          style={{
            fontSize: '12px',
            color: 'var(--zen-secondary-text)',
            margin: 0,
            fontWeight: 400,
          }}
        >
          Accounting Management
        </p>
      </div>

      {/* Organization/Entity Picker */}
      <div style={{ padding: '0 24px', marginBottom: '24px' }}>
        <OrgEntityPicker />
      </div>

      {/* Navigation Items */}
      <div className="flex-1" style={{ padding: '0 12px' }}>
        {getNavigationItems(isSME, isFirm).map(item => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.href}
              href={item.href}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '8px 12px',
                margin: '2px 0',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: 500,
                textDecoration: 'none',
                transition: 'all 0.15s ease',
                color: isActive
                  ? 'var(--zen-primary-text)'
                  : 'var(--zen-secondary-text)',
                background: isActive ? '#f9fafb' : 'transparent',
                opacity: isActive ? 1 : 0.8,
              }}
              onMouseEnter={e => {
                if (!isActive) {
                  e.currentTarget.style.background = '#f9fafb'
                  e.currentTarget.style.color = 'var(--zen-primary-text)'
                  e.currentTarget.style.opacity = '1'
                }
              }}
              onMouseLeave={e => {
                if (!isActive) {
                  e.currentTarget.style.background = 'transparent'
                  e.currentTarget.style.color = 'var(--zen-secondary-text)'
                  e.currentTarget.style.opacity = '0.8'
                }
              }}
            >
              <span style={{ opacity: 0.5 }}>{item.icon}</span>
              <span>{item.name}</span>
            </Link>
          )
        })}
      </div>

      {/* Session timeout indicator */}
      {sessionSecurity.timeoutInfo && sessionSecurity.isActive && (
        <div style={{ padding: '0 24px', marginBottom: '16px' }}>
          <SessionTimeoutIndicator
            timeRemaining={sessionSecurity.timeoutInfo.timeUntilTimeout}
            isWarning={sessionSecurity.showWarning}
            onClick={() => void sessionSecurity.extendSession()}
          />
        </div>
      )}

      {/* User section */}
      <div
        style={{
          marginTop: 'auto',
          padding: '16px 24px 0',
          borderTop: '1px solid var(--zen-border)',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div
            style={{
              fontSize: '12px',
              color: 'var(--zen-secondary-text)',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              flex: 1,
            }}
          >
            {user.email}
          </div>
          <button
            onClick={() => void handleSignOut()}
            style={{
              background: 'none',
              border: 'none',
              color: 'var(--zen-subtle-text)',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px',
              transition: 'all 0.15s ease',
              marginLeft: '8px',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.color = 'var(--zen-secondary-text)'
              e.currentTarget.style.background = '#f9fafb'
            }}
            onMouseLeave={e => {
              e.currentTarget.style.color = 'var(--zen-subtle-text)'
              e.currentTarget.style.background = 'none'
            }}
            title="Sign out"
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
            >
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
              <polyline points="16,17 21,12 16,7" />
              <line x1="21" y1="12" x2="9" y2="12" />
            </svg>
          </button>
        </div>
      </div>
    </nav>
  )
}
