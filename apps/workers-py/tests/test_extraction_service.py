"""Tests for enhanced extraction service behaviour with layout-aware inputs."""

from __future__ import annotations

import asyncio
import os
from contextlib import contextmanager
from unittest.mock import AsyncMock, Mock, patch

import pytest

from workers_py.adapters.base import ExtractionError
from workers_py.services.extract_v2 import (
    EnhancedExtractionService,
    ExtractedDocument,
    create_extraction_service,
)
from workers_py.services.extract_v2 import settings as extraction_settings


@contextmanager
def patched_settings(provider: str, mode: str):
    with patch.object(extraction_settings, "EXTRACTION_PROVIDER", provider), patch.object(
        extraction_settings, "AI_EXTRACT_RUN_MODE", mode
    ):
        yield


@pytest.fixture
def service() -> EnhancedExtractionService:
    with patched_settings("stub", "replace"):
        return EnhancedExtractionService(entity_id=123)


def test_initialization_stub_provider():
    with patched_settings("stub", "replace"):
        svc = EnhancedExtractionService(123)
        assert svc.provider == "stub"
        assert svc.run_mode == "replace"
        assert svc.shadow_adapter is None


def test_initialization_gemini_provider():
    with patched_settings("gemini_langextract", "shadow"):
        svc = EnhancedExtractionService(123)
        assert svc.provider == "gemini_langextract"
        assert svc.run_mode == "shadow"
        assert svc.shadow_adapter is not None


def test_unknown_provider_raises_error():
    with patched_settings("unknown", "replace"):
        with pytest.raises(ValueError, match="Unknown extraction provider"):
            EnhancedExtractionService(123)


async def test_process_document_success(service, sample_document_context, mock_extraction_result):
    with patch.object(service, "_extract_with_retry", new_callable=AsyncMock) as mock_extract:
        mock_extract.return_value = (mock_extraction_result, 0.95, {"provider": "stub"})

        result = await service.process_document(sample_document_context, 123)

        assert isinstance(result, ExtractedDocument)
        assert result.primary_result == mock_extraction_result
        assert result.primary_confidence == 0.95
        mock_extract.assert_awaited_once()


async def test_process_empty_document_raises_error(service, empty_document_context):
    with pytest.raises(ExtractionError, match="Empty text provided"):
        await service.process_document(empty_document_context, 123)


async def test_shadow_mode_processing(sample_document_context, mock_extraction_result):
    with patched_settings("gemini_langextract", "shadow"):
        svc = EnhancedExtractionService(123)
        primary = (mock_extraction_result, 0.9, {"provider": "gemini_flash"})
        shadow = (mock_extraction_result, 0.8, {"provider": "stub"})

        with patch.object(svc, "_extract_with_retry", new_callable=AsyncMock) as mock_extract:
            mock_extract.side_effect = [primary, shadow]
            result = await svc.process_document(sample_document_context, 123)

            assert result.primary_confidence == 0.9
            assert result.shadow_confidence == 0.8
            assert mock_extract.await_count == 2


async def test_extract_with_retry_success(service, sample_document_context, mock_extraction_result):
    adapter = Mock()
    adapter.extract = AsyncMock(return_value=(mock_extraction_result, 0.9, {}))

    result = await service._extract_with_retry(adapter, sample_document_context)

    assert result == (mock_extraction_result, 0.9, {})
    adapter.extract.assert_awaited_once_with(sample_document_context)


async def test_extract_with_retry_timeout(service, sample_document_context):
    adapter = Mock()
    adapter.extract = AsyncMock(side_effect=asyncio.TimeoutError())

    with patch("workers_py.services.extract_v2.settings.AI_EXTRACTION_RETRIES", 1):
        with pytest.raises(ExtractionError, match="failed after"):
            await service._extract_with_retry(adapter, sample_document_context)


async def test_extract_with_retry_exception(service, sample_document_context):
    adapter = Mock()
    adapter.extract = AsyncMock(side_effect=RuntimeError("API error"))

    with patch("workers_py.services.extract_v2.settings.AI_EXTRACTION_RETRIES", 1):
        with pytest.raises(ExtractionError, match="failed after"):
            await service._extract_with_retry(adapter, sample_document_context)


def test_enhance_with_templates_matching_vat(service, mock_extraction_result):
    mock_extraction_result.supplier.vat = "BE0123456789"
    mock_extraction_result.lines[0].account_hint = None

    templates = {"BE0123456789": {"default_account_id": 6100}}
    enhanced = service._enhance_with_templates(mock_extraction_result, templates)

    assert enhanced.lines[0].account_hint == 6100


def test_should_auto_suggest_threshold():
    with patch.object(extraction_settings, "AI_CONFIDENCE_THRESHOLD", 0.7):
        svc = EnhancedExtractionService(123)
        assert svc.should_auto_suggest(0.71) is True
        assert svc.should_auto_suggest(0.5) is False


def test_get_extraction_summary(service, mock_extraction_result):
    doc = ExtractedDocument(
        primary_result=mock_extraction_result,
        primary_confidence=0.9,
        primary_metadata={"provider": "stub"},
        text_length=120,
        processing_time=1.2,
    )

    summary = service.get_extraction_summary(doc)

    assert summary["entity_id"] == 123
    assert summary["primary"]["can_auto_suggest"] in {True, False}
    assert summary["primary"]["line_count"] == len(mock_extraction_result.lines)


def test_create_extraction_service_factory():
    with patch.dict(os.environ, {"EXTRACTION_PROVIDER": "stub"}, clear=False):
        svc = create_extraction_service(456)
        assert isinstance(svc, EnhancedExtractionService)
        assert svc.entity_id == 456
