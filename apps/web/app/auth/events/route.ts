import { NextResponse } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { sessionManager } from '@/lib/session-security'
import type { Session } from '@supabase/supabase-js'

export async function POST(request: Request) {
  try {
    const supabase = createSecureServerClient()
    const requestData: unknown = await request.json()
    if (
      typeof requestData !== 'object' ||
      requestData === null ||
      !('event' in requestData)
    ) {
      return NextResponse.json(
        { success: false, error: 'invalid_payload' },
        { status: 400 }
      )
    }
    const { event, session } = requestData as {
      event: string
      session: unknown
    }

    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      // Set/update the session cookies for SSR (middleware)
      // Only set session if it matches the expected shape
      // supabase-js Session type is available; fall back gracefully if not provided
      if (session && typeof session === 'object') {
        await supabase.auth.setSession(session as Session)
      }

      // Initialize/update session security manager for this session
      try {
        const s = session
        const token =
          s &&
          typeof s === 'object' &&
          'access_token' in s &&
          typeof (s as Record<string, unknown>).access_token === 'string'
            ? ((s as Record<string, unknown>).access_token as string)
            : undefined
        const userId =
          s &&
          typeof s === 'object' &&
          'user' in s &&
          typeof (s as Record<string, unknown>).user === 'object' &&
          (s as Record<string, unknown>).user !== null &&
          'id' in (s as { user?: Record<string, unknown> }).user! &&
          typeof (
            (s as { user?: Record<string, unknown> }).user as Record<
              string,
              unknown
            >
          ).id === 'string'
            ? ((s as { user?: { id?: string } }).user!.id as string)
            : undefined
        if (token && userId) {
          const existing = sessionManager.getSession(token)
          if (!existing) {
            await sessionManager.initializeSession(token, userId, {
              headers: request.headers,
            })
          } else {
            sessionManager.updateActivity(token, {
              headers: request.headers,
            })
          }
        }
      } catch (e) {
        console.warn('Auth events: session manager init failed', e)
      }
    }

    if (event === 'SIGNED_OUT') {
      await supabase.auth.signOut()
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Auth callback error:', error)
    return NextResponse.json(
      { success: false, error: 'callback_failed' },
      { status: 400 }
    )
  }
}

export function OPTIONS() {
  return NextResponse.json({})
}
