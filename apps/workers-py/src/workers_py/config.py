"""Configuration management using pydantic settings."""

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    model_config = SettingsConfigDict(
        env_file=[".env.local", ".env"], env_file_encoding="utf-8", case_sensitive=False, extra="allow"
    )

    # Application Settings
    DEBUG: bool = Field(default=False, description="Debug mode")
    LOG_LEVEL: str = Field(default="info", description="Logging level")
    ENVIRONMENT: str = Field(default="development", description="Environment name")

    # Database Configuration
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/ledgerly",
        description="Database connection URL",
    )
    DATABASE_POOL_SIZE: int = Field(default=10, description="Database pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, description="Database max overflow")

    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis URL")
    REDIS_DB: int = Field(default=0, description="Redis database number")

    # Celery Configuration
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/0", description="Celery broker URL"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/0", description="Celery result backend URL"
    )
    CELERY_TASK_SERIALIZER: str = Field(
        default="json", description="Celery task serializer"
    )
    CELERY_RESULT_SERIALIZER: str = Field(
        default="json", description="Celery result serializer"
    )
    CELERY_ACCEPT_CONTENT: list[str] = Field(
        default=["json"], description="Celery accepted content types"
    )
    CELERY_TIMEZONE: str = Field(default="UTC", description="Celery timezone")

    # External Services
    SUPABASE_URL: str | None = Field(default=None, description="Supabase project URL")
    SUPABASE_SERVICE_ROLE_KEY: str | None = Field(
        default=None, description="Supabase service role key"
    )

    # File Storage
    MAX_FILE_SIZE_MB: int = Field(default=50, description="Maximum file size in MB")
    ALLOWED_FILE_TYPES: list[str] = Field(
        default=["pdf", "png", "jpg", "jpeg", "tiff"],
        description="Allowed file types for processing",
    )

    # AI/ML Configuration
    SENTENCE_TRANSFORMERS_MODEL: str = Field(
        default="all-MiniLM-L6-v2", description="Sentence transformers model name"
    )
    OCR_ENGINE: str = Field(default="tesseract", description="OCR engine to use")
    OCR_LANGUAGE: str = Field(default="eng", description="OCR language")

    # AI Extraction Configuration
    EXTRACTION_PROVIDER: str = Field(default="stub", description="AI extraction provider")
    AI_EXTRACT_RUN_MODE: str = Field(default="shadow", description="AI extraction run mode")
    GEMINI_API_KEY: str | None = Field(default=None, description="Gemini API key")
    GEMINI_MODEL: str = Field(default="gemini-2.5-flash", description="Gemini model")
    AI_EXTRACTION_TIMEOUT: int = Field(default=20, description="AI extraction timeout")
    AI_EXTRACTION_RETRIES: int = Field(default=2, description="AI extraction retries")
    AI_CONFIDENCE_THRESHOLD: float = Field(default=0.7, description="AI confidence threshold")
    GEMINI_MAX_CONCURRENCY: int = Field(
        default=4, description="Maximum concurrent Gemini region classifications"
    )
    # Reconciliation / reasoning settings
    AI_RECONCILIATION_MODE: str = Field(
        default="deterministic",
        description="Reconciliation mode: deterministic | llm | llm+deterministic",
    )
    VALIDATOR_STRICT_TOTALS: bool = Field(
        default=True,
        description="If true, line/header totals mismatches are errors instead of warnings",
    )

    # Security
    SECRET_KEY: str = Field(
        default="your-secret-key-change-this-in-production",
        description="Secret key for encryption",
    )
    CORS_ORIGINS: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        description="CORS allowed origins",
    )

    # Performance
    MAX_WORKERS: int = Field(default=4, description="Maximum worker processes")
    TASK_TIMEOUT: int = Field(default=300, description="Task timeout in seconds")

    # Additional settings for different environments
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT.lower() in ("development", "dev")

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT.lower() in ("production", "prod")

    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.ENVIRONMENT.lower() in ("testing", "test")

    def get_database_config(self) -> dict:
        """Get database configuration dictionary."""
        return {
            "url": self.DATABASE_URL,
            "pool_size": self.DATABASE_POOL_SIZE,
            "max_overflow": self.DATABASE_MAX_OVERFLOW,
        }

    def get_redis_config(self) -> dict:
        """Get Redis configuration dictionary."""
        return {
            "url": self.REDIS_URL,
            "db": self.REDIS_DB,
        }

    def get_celery_config(self) -> dict:
        """Get Celery configuration dictionary."""
        return {
            "broker_url": self.CELERY_BROKER_URL,
            "result_backend": self.CELERY_RESULT_BACKEND,
            "task_serializer": self.CELERY_TASK_SERIALIZER,
            "result_serializer": self.CELERY_RESULT_SERIALIZER,
            "accept_content": self.CELERY_ACCEPT_CONTENT,
            "timezone": self.CELERY_TIMEZONE,
        }


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings instance."""
    return settings
