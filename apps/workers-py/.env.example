# Application Settings
DEBUG=false
LOG_LEVEL=info
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/ledgerly
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_DB=0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC

# External Services
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# File Storage
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=["pdf", "png", "jpg", "jpeg", "tiff"]

# AI/ML Configuration
SENTENCE_TRANSFORMERS_MODEL=all-MiniLM-L6-v2
OCR_ENGINE=tesseract
OCR_LANGUAGE=eng

# AI Extraction Configuration
EXTRACTION_PROVIDER=stub  # stub|gemini_langextract
GEMINI_MODEL=gemini-2.5-flash  # gemini-2.5-flash|gemini-2.5-pro
AI_EXTRACT_RUN_MODE=shadow  # shadow|replace|disabled

# Google AI Configuration (choose one authentication method)
# Option A: Google AI Studio (simple)
GEMINI_API_KEY=your-gemini-api-key-here

# Option B: Vertex AI (enterprise) - uncomment if using
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_LOCATION=europe-west1
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# AI Extraction Settings
AI_EXTRACTION_TIMEOUT=20  # seconds
AI_EXTRACTION_RETRIES=2
AI_CONFIDENCE_THRESHOLD=0.7

# Security
SECRET_KEY=your-secret-key-change-this-in-production
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Performance
MAX_WORKERS=4
TASK_TIMEOUT=300
