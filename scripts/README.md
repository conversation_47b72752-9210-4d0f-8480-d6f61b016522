# Document Processing Reset Scripts

This directory contains scripts to reset document processing data and clean extracted information from the database. These scripts are useful for testing, development, and maintenance.

## 🎯 What These Scripts Do

The reset scripts perform the following operations:

1. **Reset inbox documents** - Sets all documents back to `'uploaded'` status (unprocessed)
2. **Clear extracted data** - Removes all AI-extracted invoices, journals, and journal lines
3. **Clean processing results** - Clears document processing results and task status
4. **Remove cache** - Clears file processing cache and embeddings
5. **Preserve originals** - Keeps original document files and basic metadata intact

## 📁 Available Scripts

### 1. Python Script (Recommended)
- **File**: `reset-documents.py`
- **Direct database connection using psycopg2**
- **Handles missing tables/columns gracefully**
- **Detailed progress reporting**

### 2. Shell Script Wrapper
- **File**: `reset-documents.sh`
- **Wrapper around the Python script**
- **Automatic dependency checking**
- **Environment variable loading**

### 3. Node.js Script (Alternative)
- **File**: `reset-documents.js`
- **Uses Supabase client library**
- **Requires Node.js environment**

## 🚀 Usage

### Quick Start (Recommended)

```bash
# Reset local database
npm run reset-docs

# Reset staging database
npm run reset-docs:staging

# Reset production database (requires confirmation)
npm run reset-docs:prod
```

### Direct Script Usage

```bash
# Python script (recommended)
python3 scripts/reset-documents.py              # Local
python3 scripts/reset-documents.py --staging    # Staging
python3 scripts/reset-documents.py --prod       # Production

# Shell wrapper
./scripts/reset-documents.sh                    # Local
./scripts/reset-documents.sh staging            # Staging
./scripts/reset-documents.sh prod               # Production
```

## 🔧 Prerequisites

### For Python Script
- Python 3.6+
- `psycopg2-binary` package

The shell script will automatically install `psycopg2-binary` if missing.

### For Node.js Script
- Node.js 18+
- `@supabase/supabase-js` package

## 🌍 Environment Configuration

### Local Development
- Uses local Supabase instance (localhost:54322)
- Default credentials: postgres/postgres

### Staging
- Uses `STAGING_DATABASE_URL` or `DATABASE_URL` environment variable
- Requires staging database credentials in `.env.local`

### Production
- Uses `PRODUCTION_DATABASE_URL` or `PROD_DATABASE_URL` environment variable
- Requires explicit confirmation before execution
- **⚠️ Use with extreme caution!**

## 📊 What Gets Reset

### Tables Affected
- `inbox_documents` - Status reset to 'uploaded'
- `document_processing_results` - Completely cleared
- `task_status` - Completely cleared
- `file_processing_cache` - Completely cleared
- `invoice_embeddings` - Completely cleared (if exists)
- `journal_lines` - Completely cleared
- `journals` - Completely cleared
- `invoices` - Completely cleared
- `documents` - Associations cleared (files preserved)

### What's Preserved
- Original document files in storage
- Document metadata and file references
- User accounts and entity data
- System configuration

## 🛡️ Safety Features

### Production Protection
- Requires explicit confirmation for production resets
- Must type "CONFIRM PRODUCTION RESET" exactly
- Clear warnings about data loss

### Error Handling
- Gracefully handles missing tables/columns
- Continues execution even if some operations fail
- Detailed error reporting

### Dry Run Information
- Shows current database state before reset
- Reports final state after reset
- Detailed summary of all operations

## 📝 Example Output

```
🔄 Starting document reset for local environment...
✅ Connected to local database

📊 Current database state:
   inbox_documents: 5 rows
   invoices: 3 rows
   journals: 2 rows

🧹 Performing reset operations...
📝 Reset inbox documents to uploaded status...
   ✅ 5 rows affected
📝 Clear invoices...
   ✅ 3 rows affected
📝 Clear journals...
   ✅ 2 rows affected

📋 Reset Summary:
============================================================
✅ Reset inbox documents to uploaded status: 5 rows
✅ Clear invoices: 3 rows
✅ Clear journals: 2 rows
============================================================
🎉 Reset complete! Total rows affected: 10
```

## 🔄 Integration with Development Workflow

### After Reset
1. Start development servers: `./dev-setup.sh`
2. Open frontend: http://localhost:3000
3. Upload documents to test processing pipeline

### Common Use Cases
- **Before testing**: Clean slate for document processing tests
- **After failed processing**: Reset stuck or failed documents
- **Development setup**: Prepare clean environment for new features
- **Demo preparation**: Clean database for demonstrations

## 🚨 Important Notes

1. **Backup First**: Always backup important data before running reset scripts
2. **Test Environment**: Prefer testing on local/staging before production
3. **Service Dependencies**: Ensure database is accessible and services are running
4. **Environment Variables**: Verify correct environment variables are loaded
5. **Permissions**: Ensure database user has necessary permissions for all operations

## 🐛 Troubleshooting

### Common Issues

**Connection Failed**
```bash
# Check if database is running
./dev-setup.sh --check

# Verify environment variables
echo $DATABASE_URL
```

**Missing Dependencies**
```bash
# Install Python dependencies
pip install psycopg2-binary

# Or use the shell script which auto-installs
./scripts/reset-documents.sh
```

**Permission Denied**
```bash
# Make scripts executable
chmod +x scripts/reset-documents.sh scripts/reset-documents.py
```

### Getting Help

```bash
# Show help for Python script
python3 scripts/reset-documents.py --help

# Show help for shell script
./scripts/reset-documents.sh --help
```
