from workers_py.extraction.models import OcrResult, DocumentContext, DocumentPage, BoundingBox, OcrLine, OcrToken
from workers_py.models import ExtractionResult, SupplierInfo, InvoiceInfo, TotalsInfo, PaymentInstructions, LineItem, VatRate
from workers_py.services.reconcile import reconcile_extraction


def make_doc(text: str) -> DocumentContext:
    page = DocumentPage(number=1, width=1000, height=1000, image_bytes=b"", preview_base64="")
    line = OcrLine(id="p1_l1", text=text, bbox=BoundingBox(0, 0, 1, 1), page=1)
    ocr = OcrResult(text=text, tokens=[], lines=[line], pages=[page])
    return DocumentContext(ocr=ocr, mime_type="application/pdf")


def test_reconcile_multiline_invoice():
    # Simulate OCR text similar to the screenshot (Dutch headers)
    text = (
        "Omschrijving      Aantal  Prijs ex btw  BTW %  Bedrag ex btw  Bedrag incl btw\n"
        "Internet Fibre - Forfait   3      60,97        21%     182,92        221,33\n"
        "Internet Glasvezel - Abonnement  1   53,26   21%   53,26   64,44\n"
        "Internet Glasvezel - Abonnement  3   57,39   21%   172,17  208,33\n"
        "Totaal ex btw: 408,35   TVA/Btw: 85,75   Totaal incl btw: 494,10"
    )

    doc = make_doc(text)

    extraction = ExtractionResult(
        supplier=SupplierInfo(name="Bruges Print NV", vat="BE0204298801"),
        customer=None,
        invoice=InvoiceInfo(number="SUP-2025-0002", issue_date="2025-08-02", due_date="2025-08-16", currency="EUR"),
        totals=TotalsInfo(currency="EUR"),
        payment_instructions=PaymentInstructions(iban="****************"),
        lines=[
            LineItem(description="Internet Fibre - Forfait", quantity="3", unit_price="60.97", vat_rate=VatRate.STANDARD),
            LineItem(description="Internet Glasvezel - Abonnement", quantity="1", unit_price="53.26", vat_rate=VatRate.STANDARD),
            LineItem(description="Internet Glasvezel - Abonnement", quantity="3", unit_price="57.39", vat_rate=VatRate.STANDARD),
        ],
    )

    reconciled = reconcile_extraction(doc, extraction)

    # Expect line totals as in OCR
    nets = [li.line_net for li in reconciled.lines]
    vats = [li.line_vat for li in reconciled.lines]

    assert nets == ["182.92", "53.26", "172.17"]
    assert vats == ["38.41", "11.18", "36.16"]

    assert reconciled.invoice.net == "408.35"
    assert reconciled.invoice.vat == "85.75"
    assert reconciled.invoice.gross == "494.10"
