'use client'

import { useState, useEffect, useCallback } from 'react'
import { useCurrentEntity } from '@/contexts/OrgEntityContext'
import { parseJsonSafe } from '@/lib/http/api'

interface VATEntry {
  grid_code: string
  direction: string
  base_total: number
  vat_total: number
}

interface VATSummary {
  output: {
    base_total: number
    vat_total: number
  }
  input: {
    base_total: number
    vat_total: number
  }
  net_vat_payable: number
}

interface VATPreviewData {
  period: {
    start: string
    end: string
  }
  entries: VATEntry[]
  summary: VATSummary
}

interface PeriodOption {
  label: string
  start: string
  end: string
}

export default function VATPage() {
  const selectedEntity = useCurrentEntity()
  const [vatData, setVatData] = useState<VATPreviewData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodOption>({
    label: 'Current Quarter',
    start: getCurrentQuarterStart(),
    end: getCurrentQuarterEnd(),
  })
  const [isExporting, setIsExporting] = useState(false)

  // Generate period options
  const periodOptions: PeriodOption[] = [
    {
      label: 'Current Quarter',
      start: getCurrentQuarterStart(),
      end: getCurrentQuarterEnd(),
    },
    {
      label: 'Previous Quarter',
      start: getPreviousQuarterStart(),
      end: getPreviousQuarterEnd(),
    },
    {
      label: 'Current Month',
      start: getCurrentMonthStart(),
      end: getCurrentMonthEnd(),
    },
    {
      label: 'Previous Month',
      start: getPreviousMonthStart(),
      end: getPreviousMonthEnd(),
    },
  ]

  // Load VAT data
  const loadVATData = useCallback(async () => {
    if (!selectedEntity?.entity_id) {
      setError('No entity selected')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        start: selectedPeriod.start,
        end: selectedPeriod.end,
      })

      const response = await fetch(
        `/api/entities/${selectedEntity.entity_id}/vat/preview?${params.toString()}`
      )
      const result = await parseJsonSafe<{
        success?: boolean
        data?: VATPreviewData
        error?: string
      }>(response)

      if (!result || result.success !== true) {
        throw new Error(result?.error || 'Failed to load VAT data')
      }

      if (result?.data) {
        setVatData(result.data)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load VAT data')
    } finally {
      setLoading(false)
    }
  }, [selectedEntity, selectedPeriod])

  // Export CSV
  const exportCSV = async () => {
    if (!selectedEntity?.entity_id) return

    setIsExporting(true)
    try {
      const params = new URLSearchParams({
        start: selectedPeriod.start,
        end: selectedPeriod.end,
      })

      const response = await fetch(
        `/api/entities/${selectedEntity.entity_id}/vat/export.csv?${params.toString()}`
      )

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(errorText || 'Failed to export CSV')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `vat-export-${selectedEntity.entity_id}-${selectedPeriod.start}-to-${selectedPeriod.end}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export CSV')
    } finally {
      setIsExporting(false)
    }
  }

  // Load data on mount and when period/entity changes
  useEffect(() => {
    void loadVATData()
  }, [loadVATData])

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-BE', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount)
  }

  // Group entries by direction
  const outputEntries =
    vatData?.entries.filter(e => e.direction === 'output') || []
  const inputEntries =
    vatData?.entries.filter(e => e.direction === 'input') || []

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          VAT Management
        </h1>
        <p className="text-gray-600">
          Live VAT preview and export for {selectedEntity?.entity_name}
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Period Selection */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex gap-2">
          <select
            value={JSON.stringify(selectedPeriod)}
            onChange={e =>
              setSelectedPeriod(JSON.parse(e.target.value) as PeriodOption)
            }
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            disabled={loading}
          >
            {periodOptions.map((option, index) => (
              <option key={index} value={JSON.stringify(option)}>
                {option.label} ({option.start} to {option.end})
              </option>
            ))}
          </select>
        </div>

        <div className="flex gap-2">
          <button
            onClick={() => void loadVATData()}
            disabled={loading || !selectedEntity}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
          <button
            onClick={() => void exportCSV()}
            disabled={isExporting || !vatData || !selectedEntity}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isExporting ? 'Exporting...' : 'Export CSV'}
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="text-gray-500">Loading VAT data...</div>
        </div>
      ) : vatData ? (
        <>
          {/* VAT Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
            <div className="bg-white rounded-lg shadow p-4 border border-gray-200">
              <p className="text-sm font-medium text-gray-500">VAT Collected</p>
              <p className="text-xl font-bold text-green-600 mt-1">
                {formatCurrency(vatData.summary.output.vat_total)}
              </p>
              <p className="text-xs text-gray-500 mt-1">Output VAT on sales</p>
            </div>
            <div className="bg-white rounded-lg shadow p-4 border border-gray-200">
              <p className="text-sm font-medium text-gray-500">VAT Paid</p>
              <p className="text-xl font-bold text-blue-600 mt-1">
                {formatCurrency(vatData.summary.input.vat_total)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Input VAT on purchases
              </p>
            </div>
            <div className="bg-white rounded-lg shadow p-4 border border-gray-200">
              <p className="text-sm font-medium text-gray-500">Net VAT Due</p>
              <p
                className={`text-xl font-bold mt-1 ${
                  vatData.summary.net_vat_payable >= 0
                    ? 'text-red-600'
                    : 'text-green-600'
                }`}
              >
                {formatCurrency(Math.abs(vatData.summary.net_vat_payable))}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {vatData.summary.net_vat_payable >= 0
                  ? 'Amount to pay'
                  : 'Amount to reclaim'}
              </p>
            </div>
            <div className="bg-white rounded-lg shadow p-4 border border-gray-200">
              <p className="text-sm font-medium text-gray-500">Period</p>
              <p className="text-xl font-bold text-gray-900 mt-1">
                {selectedPeriod.label}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {vatData.period.start} to {vatData.period.end}
              </p>
            </div>
          </div>

          {/* VAT Return Details */}
          <div className="grid gap-6 lg:grid-cols-2 mb-8">
            {/* Output VAT */}
            <div className="bg-white rounded-lg shadow border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Output VAT (Sales)
                </h2>
              </div>

              <div className="p-6">
                {outputEntries.length > 0 ? (
                  <div className="space-y-4">
                    {outputEntries.map((entry, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
                      >
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {entry.grid_code}
                          </p>
                          <p className="text-xs text-gray-500">
                            Grid: {entry.grid_code}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-900">
                            Base: {formatCurrency(entry.base_total)}
                          </p>
                          <p className="text-sm font-medium text-gray-900">
                            VAT: {formatCurrency(entry.vat_total)}
                          </p>
                        </div>
                      </div>
                    ))}

                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-900">
                          Total Output VAT
                        </span>
                        <span className="font-bold text-gray-900">
                          {formatCurrency(vatData.summary.output.vat_total)}
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">
                    No output VAT entries for this period
                  </p>
                )}
              </div>
            </div>

            {/* Input VAT */}
            <div className="bg-white rounded-lg shadow border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Input VAT (Purchases)
                </h2>
              </div>

              <div className="p-6">
                {inputEntries.length > 0 ? (
                  <div className="space-y-4">
                    {inputEntries.map((entry, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
                      >
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {entry.grid_code}
                          </p>
                          <p className="text-xs text-gray-500">
                            Grid: {entry.grid_code}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-900">
                            Base: {formatCurrency(entry.base_total)}
                          </p>
                          <p className="text-sm font-medium text-gray-900">
                            VAT: {formatCurrency(entry.vat_total)}
                          </p>
                        </div>
                      </div>
                    ))}

                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-900">
                          Total Input VAT
                        </span>
                        <span className="font-bold text-gray-900">
                          {formatCurrency(vatData.summary.input.vat_total)}
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">
                    No input VAT entries for this period
                  </p>
                )}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">No VAT data available</p>
        </div>
      )}

      {/* Current VAT Rates Reference */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Current VAT Rates (Belgium)
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Reference rates for the current period
          </p>
        </div>

        <div className="p-6">
          <div className="grid gap-4 md:grid-cols-2">
            {[
              {
                rate: '21%',
                description: 'Standard rate',
                examples: 'Most goods and services',
              },
              {
                rate: '12%',
                description: 'Reduced rate',
                examples: 'Restaurant meals, social housing',
              },
              {
                rate: '6%',
                description: 'Reduced rate',
                examples: 'Food, books, medicines',
              },
              {
                rate: '0%',
                description: 'Zero rate / Exempt',
                examples: 'Exports, financial services',
              },
            ].map((item, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 rounded-lg"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-lg font-bold text-gray-900">
                    {item.rate}
                  </span>
                  <span className="text-sm text-gray-600">
                    {item.description}
                  </span>
                </div>
                <p className="text-xs text-gray-500">{item.examples}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper functions for date calculations
function getCurrentQuarterStart(): string {
  const now = new Date()
  const quarter = Math.floor(now.getMonth() / 3)
  const year = now.getFullYear()
  const month = quarter * 3
  return new Date(year, month, 1).toISOString().split('T')[0]
}

function getCurrentQuarterEnd(): string {
  const now = new Date()
  const quarter = Math.floor(now.getMonth() / 3)
  const year = now.getFullYear()
  const month = quarter * 3 + 3
  return new Date(year, month, 0).toISOString().split('T')[0]
}

function getPreviousQuarterStart(): string {
  const now = new Date()
  const quarter = Math.floor(now.getMonth() / 3)
  let year = now.getFullYear()
  let prevQuarter = quarter - 1

  if (prevQuarter < 0) {
    prevQuarter = 3
    year -= 1
  }

  const month = prevQuarter * 3
  return new Date(year, month, 1).toISOString().split('T')[0]
}

function getPreviousQuarterEnd(): string {
  const now = new Date()
  const quarter = Math.floor(now.getMonth() / 3)
  let year = now.getFullYear()
  let prevQuarter = quarter - 1

  if (prevQuarter < 0) {
    prevQuarter = 3
    year -= 1
  }

  const month = prevQuarter * 3 + 3
  return new Date(year, month, 0).toISOString().split('T')[0]
}

function getCurrentMonthStart(): string {
  const now = new Date()
  return new Date(now.getFullYear(), now.getMonth(), 1)
    .toISOString()
    .split('T')[0]
}

function getCurrentMonthEnd(): string {
  const now = new Date()
  return new Date(now.getFullYear(), now.getMonth() + 1, 0)
    .toISOString()
    .split('T')[0]
}

function getPreviousMonthStart(): string {
  const now = new Date()
  let month = now.getMonth() - 1
  let year = now.getFullYear()

  if (month < 0) {
    month = 11
    year -= 1
  }

  return new Date(year, month, 1).toISOString().split('T')[0]
}

function getPreviousMonthEnd(): string {
  const now = new Date()
  let month = now.getMonth() - 1
  let year = now.getFullYear()

  if (month < 0) {
    month = 11
    year -= 1
  }

  return new Date(year, month + 1, 0).toISOString().split('T')[0]
}
