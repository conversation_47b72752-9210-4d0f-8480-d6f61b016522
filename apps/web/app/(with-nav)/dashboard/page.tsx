'use client'

import React, { useState, CSSProperties } from 'react'
import { useRouter } from 'next/navigation'
import { useDashboardMetrics } from '@/hooks/useDashboardMetrics'
import { useVATSummary } from '@/hooks/useVATSummary'
import AutomationHelpers from '@/components/AutomationHelpers'

// Zen UI Theme - Following the style guide exactly (same as inbox)
const zenTheme = {
  // Primary Colors - CORRECTED to match style guide
  bg: '#FBFAF5', // soft cream background (was wrong before)
  surface: '#FFFFFC', // pure white for cards
  primaryText: '#1a1a1a', // near black
  secondaryText: '#6b7280', // warm gray
  subtleText: '#9ca3af', // light gray

  // Borders and Surfaces
  border: '#f3f4f6', // subtle border
  borderHover: '#e5e7eb', // slightly darker on hover

  // Accent Colors (use sparingly)
  success: '#10b981', // soft green
  warning: '#f59e0b', // warm amber
  error: '#ef4444', // soft red
  primaryAction: '#3b82f6', // calm blue (not black for primary action)

  // Shadows and Effects
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)', // very subtle
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', // slightly more on hover
}

// Zen UI Style functions (same as inbox)
const getPageStyle = (theme: typeof zenTheme): CSSProperties => ({
  color: theme.primaryText,
  fontFamily:
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif",
  lineHeight: 1.5, // improved readability
})

const getCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  background: theme.surface,
  border: `1px solid ${theme.border}`,
  borderRadius: '8px', // reduced for cleaner look
  boxShadow: theme.shadow,
  transition: 'all 0.15s ease', // subtle interaction
})

const getButtonStyle = (
  theme: typeof zenTheme,
  variant: 'primary' | 'secondary' = 'secondary'
): CSSProperties => ({
  padding: '8px 16px',
  borderRadius: '6px',
  fontSize: '14px',
  fontWeight: 500,
  border: variant === 'primary' ? 'none' : `1px solid ${theme.border}`,
  background: variant === 'primary' ? '#111827' : theme.surface, // Black background for primary
  color: variant === 'primary' ? '#ffffff' : theme.primaryText,
  cursor: 'pointer',
  transition: 'all 0.15s ease',
})

const getMetricCardStyle = (theme: typeof zenTheme): CSSProperties => ({
  ...getCardStyle(theme),
  padding: '24px',
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
})

const getMetricLabelStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '12px',
  fontWeight: 500,
  color: theme.secondaryText,
  margin: 0,
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
})

const getMetricValueStyle = (theme: typeof zenTheme): CSSProperties => ({
  fontSize: '24px',
  fontWeight: 600,
  color: theme.primaryText,
  margin: 0,
})

const getActionButtonStyle = (theme: typeof zenTheme): CSSProperties => ({
  ...getButtonStyle(theme, 'secondary'),
  padding: '12px 24px',
  fontSize: '14px',
  fontWeight: 500,
})

export default function DashboardPage() {
  const router = useRouter()
  const [, setNavigating] = useState<string | null>(null)

  // Data hooks
  const {
    metrics,
    loading: metricsLoading,
    error: metricsError,
  } = useDashboardMetrics()
  const { vatPosition, loading: vatLoading, error: vatError } = useVATSummary()

  const theme = zenTheme

  // Helper functions for data formatting
  const formatVATPosition = () => {
    if (vatLoading) return 'Loading...'
    if (vatError || !vatPosition) return 'Not configured'
    return `€${vatPosition.amount.toFixed(2)}`
  }

  const formatBankBalance = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError || !metrics?.bankBalance) return 'No accounts connected'
    return `€${metrics.bankBalance.total.toFixed(2)}`
  }

  const formatOpenInvoices = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError || !metrics?.openInvoices) return 'No invoices'
    return `${metrics.openInvoices.count} invoices`
  }

  const formatReconciliationTasks = () => {
    if (metricsLoading) return 'Loading...'
    if (metricsError || !metrics?.reconciliationTasks) return 'All caught up'
    return `${metrics.reconciliationTasks.count} tasks`
  }

  // Navigation handlers
  const handleNavigateToInbox = () => {
    setNavigating('inbox')
    router.push('/inbox')
  }

  const handleNavigateToVAT = () => {
    setNavigating('vat')
    router.push('/vat')
  }

  const handleNavigateToLedger = () => {
    setNavigating('ledger')
    router.push('/ledger')
  }

  return (
    <div style={getPageStyle(theme)}>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'minmax(0, 1fr) 320px',
          gap: '32px',
          maxWidth: '1400px',
          margin: '0 auto',
        }}
      >
        {/* Main Content */}
        <section>
          <div style={{ marginBottom: '32px' }}>
            <h1
              style={{
                fontSize: '32px',
                fontWeight: 600,
                color: theme.primaryText,
                margin: '0 0 8px 0',
              }}
            >
              Dashboard
            </h1>
            <p
              style={{
                fontSize: '16px',
                color: theme.secondaryText,
                margin: 0,
              }}
            >
              Overview of your business financial status
            </p>
          </div>

          {/* Action Bar */}
          <div style={getCardStyle(theme)}>
            <div
              style={{
                padding: '16px 24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <div style={{ display: 'flex', gap: '12px' }}>
                <button
                  onClick={handleNavigateToInbox}
                  style={getButtonStyle(theme, 'primary')}
                  aria-label="Upload document"
                >
                  Upload Document
                </button>
                <button
                  onClick={handleNavigateToLedger}
                  style={getButtonStyle(theme, 'secondary')}
                >
                  Create Invoice
                </button>
                <button
                  onClick={handleNavigateToLedger}
                  style={getButtonStyle(theme, 'secondary')}
                >
                  Reconcile Bank
                </button>
                <button
                  onClick={() => {
                    /* TODO: Generate Report */
                  }}
                  style={getButtonStyle(theme, 'secondary')}
                >
                  Generate Report
                </button>
              </div>
            </div>
          </div>

          {/* Metrics Grid */}
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
              gap: '16px',
              marginTop: '24px',
              marginBottom: '32px',
            }}
          >
            {/* VAT Position Card */}
            <div style={getMetricCardStyle(theme)}>
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  style={{ opacity: 0.6 }}
                >
                  <circle cx="12" cy="12" r="3" />
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
                </svg>
                <p style={getMetricLabelStyle(theme)}>Next VAT</p>
              </div>
              <p style={getMetricValueStyle(theme)}>{formatVATPosition()}</p>
              <button
                onClick={handleNavigateToVAT}
                style={{
                  ...getActionButtonStyle(theme),
                  marginTop: '8px',
                  alignSelf: 'flex-start',
                }}
              >
                Configure VAT
              </button>
            </div>

            {/* Bank Balance Card */}
            <div style={getMetricCardStyle(theme)}>
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  style={{ opacity: 0.6 }}
                >
                  <rect x="1" y="3" width="15" height="13" />
                  <polygon points="16,8 20,8 23,11 23,16 16,16" />
                  <circle cx="5.5" cy="18.5" r="2.5" />
                  <circle cx="18.5" cy="18.5" r="2.5" />
                </svg>
                <p style={getMetricLabelStyle(theme)}>Bank Balance</p>
              </div>
              <p style={getMetricValueStyle(theme)}>{formatBankBalance()}</p>
              <button
                onClick={handleNavigateToLedger}
                style={{
                  ...getActionButtonStyle(theme),
                  marginTop: '8px',
                  alignSelf: 'flex-start',
                }}
              >
                Connect Bank
              </button>
            </div>

            {/* Open Invoices Card */}
            <div style={getMetricCardStyle(theme)}>
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  style={{ opacity: 0.6 }}
                >
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                  <polyline points="14,2 14,8 20,8" />
                  <line x1="16" y1="13" x2="8" y2="13" />
                  <line x1="16" y1="17" x2="8" y2="17" />
                  <polyline points="10,9 9,9 8,9" />
                </svg>
                <p style={getMetricLabelStyle(theme)}>Open Invoices</p>
              </div>
              <p style={getMetricValueStyle(theme)}>{formatOpenInvoices()}</p>
              <button
                onClick={handleNavigateToLedger}
                style={{
                  ...getActionButtonStyle(theme),
                  marginTop: '8px',
                  alignSelf: 'flex-start',
                }}
              >
                Create Invoice
              </button>
            </div>

            {/* Pending Tasks Card */}
            <div style={getMetricCardStyle(theme)}>
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  style={{ opacity: 0.6 }}
                >
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12,6 12,12 16,14" />
                </svg>
                <p style={getMetricLabelStyle(theme)}>Pending Tasks</p>
              </div>
              <p style={getMetricValueStyle(theme)}>
                {formatReconciliationTasks()}
              </p>
              <button
                onClick={handleNavigateToInbox}
                style={{
                  ...getActionButtonStyle(theme),
                  marginTop: '8px',
                  alignSelf: 'flex-start',
                }}
              >
                View Inbox
              </button>
            </div>
          </div>

          {/* AI Inline Banners */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '16px',
              marginBottom: '32px',
            }}
          >
            {/* Welcome Banner */}
            <div
              style={{
                ...getCardStyle(theme),
                padding: '16px 20px',
                display: 'flex',
                alignItems: 'center',
                gap: '16px',
                borderLeft: '4px solid #10b981', // Green accent
              }}
            >
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#10b981',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="2"
                >
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                  <polyline points="14,2 14,8 20,8" />
                  <line x1="16" y1="13" x2="8" y2="13" />
                  <line x1="16" y1="17" x2="8" y2="17" />
                  <polyline points="10,9 9,9 8,9" />
                </svg>
              </div>
              <div style={{ flex: 1 }}>
                <h3
                  style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: theme.primaryText,
                    margin: '0 0 4px 0',
                  }}
                >
                  Welcome to BelBooks! Let&apos;s get you started
                </h3>
                <p
                  style={{
                    fontSize: '14px',
                    color: theme.secondaryText,
                    margin: 0,
                  }}
                >
                  Upload your first supplier invoice to see our AI extraction in
                  action.
                </p>
              </div>
              <button
                onClick={handleNavigateToInbox}
                style={{
                  ...getButtonStyle(theme, 'primary'),
                  fontSize: '14px',
                  padding: '8px 16px',
                }}
              >
                Upload Document
              </button>
              <button
                style={{
                  background: 'none',
                  border: 'none',
                  color: theme.subtleText,
                  cursor: 'pointer',
                  fontSize: '18px',
                  padding: '4px',
                }}
              >
                ×
              </button>
            </div>

            {/* VAT Setup Banner */}
            <div
              style={{
                ...getCardStyle(theme),
                padding: '16px 20px',
                display: 'flex',
                alignItems: 'center',
                gap: '16px',
                borderLeft: '4px solid #8b5cf6', // Purple accent
              }}
            >
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#8b5cf6',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="2"
                >
                  <circle cx="12" cy="12" r="3" />
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
                </svg>
              </div>
              <div style={{ flex: 1 }}>
                <h3
                  style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: theme.primaryText,
                    margin: '0 0 4px 0',
                  }}
                >
                  Set up VAT tracking
                </h3>
                <p
                  style={{
                    fontSize: '14px',
                    color: theme.secondaryText,
                    margin: 0,
                  }}
                >
                  Configure VAT to track your tax position in real-time and stay
                  compliant with regulations.
                </p>
              </div>
              <button
                onClick={handleNavigateToVAT}
                style={{
                  ...getButtonStyle(theme, 'primary'),
                  fontSize: '14px',
                  padding: '8px 16px',
                }}
              >
                Configure VAT
              </button>
              <button
                style={{
                  background: 'none',
                  border: 'none',
                  color: theme.subtleText,
                  cursor: 'pointer',
                  fontSize: '18px',
                  padding: '4px',
                }}
              >
                ×
              </button>
            </div>

            {/* Bank Connection Banner */}
            <div
              style={{
                ...getCardStyle(theme),
                padding: '16px 20px',
                display: 'flex',
                alignItems: 'center',
                gap: '16px',
                borderLeft: '4px solid #f59e0b', // Orange accent
              }}
            >
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#f59e0b',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="2"
                >
                  <rect x="1" y="3" width="15" height="13" />
                  <polygon points="16,8 20,8 23,11 23,16 16,16" />
                  <circle cx="5.5" cy="18.5" r="2.5" />
                  <circle cx="18.5" cy="18.5" r="2.5" />
                </svg>
              </div>
              <div style={{ flex: 1 }}>
                <h3
                  style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: theme.primaryText,
                    margin: '0 0 4px 0',
                  }}
                >
                  Connect your bank account
                </h3>
                <p
                  style={{
                    fontSize: '14px',
                    color: theme.secondaryText,
                    margin: 0,
                  }}
                >
                  Enable automatic transaction matching and reconciliation for
                  streamlined bookkeeping.
                </p>
              </div>
              <button
                onClick={handleNavigateToLedger}
                style={{
                  ...getButtonStyle(theme, 'primary'),
                  fontSize: '14px',
                  padding: '8px 16px',
                }}
              >
                Connect Bank
              </button>
              <button
                style={{
                  background: 'none',
                  border: 'none',
                  color: theme.subtleText,
                  cursor: 'pointer',
                  fontSize: '18px',
                  padding: '4px',
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Getting Started Section */}
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '24px',
              marginBottom: '32px',
            }}
          >
            <div style={getCardStyle(theme)}>
              <div
                style={{
                  padding: '24px',
                  borderBottom: `1px solid ${theme.border}`,
                }}
              >
                <h2
                  style={{
                    fontSize: '20px',
                    fontWeight: 600,
                    color: theme.primaryText,
                    margin: 0,
                  }}
                >
                  Getting Started
                </h2>
                <p
                  style={{
                    fontSize: '14px',
                    color: theme.secondaryText,
                    margin: '4px 0 0 0',
                  }}
                >
                  Complete these steps to get the most out of BelBooks
                </p>
                <p
                  style={{
                    fontSize: '12px',
                    color: theme.subtleText,
                    margin: '4px 0 0 0',
                  }}
                >
                  0 of 3 completed
                </p>
              </div>

              <div style={{ padding: '24px' }}>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '24px',
                  }}
                >
                  {/* Upload Document Step */}
                  <div style={{ display: 'flex', gap: '16px' }}>
                    <div style={{ flexShrink: 0 }}>
                      <div
                        style={{
                          width: '40px',
                          height: '40px',
                          backgroundColor: theme.surface,
                          border: `1px solid ${theme.border}`,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          style={{ opacity: 0.6 }}
                        >
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                          <polyline points="14,2 14,8 20,8" />
                          <line x1="16" y1="13" x2="8" y2="13" />
                          <line x1="16" y1="17" x2="8" y2="17" />
                          <polyline points="10,9 9,9 8,9" />
                        </svg>
                      </div>
                    </div>
                    <div style={{ flex: 1 }}>
                      <h3
                        style={{
                          fontSize: '16px',
                          fontWeight: 600,
                          color: theme.primaryText,
                          margin: '0 0 4px 0',
                        }}
                      >
                        Upload your first supplier invoice
                      </h3>
                      <p
                        style={{
                          fontSize: '14px',
                          color: theme.secondaryText,
                          margin: '0 0 12px 0',
                        }}
                      >
                        Start by uploading a PDF or photo of a supplier invoice
                      </p>
                      <button
                        onClick={handleNavigateToInbox}
                        style={{
                          ...getButtonStyle(theme, 'primary'),
                          fontSize: '12px',
                          padding: '6px 12px',
                        }}
                      >
                        Upload Document
                      </button>
                    </div>
                  </div>

                  {/* Connect Bank Step */}
                  <div style={{ display: 'flex', gap: '16px' }}>
                    <div style={{ flexShrink: 0 }}>
                      <div
                        style={{
                          width: '40px',
                          height: '40px',
                          backgroundColor: theme.surface,
                          border: `1px solid ${theme.border}`,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          style={{ opacity: 0.6 }}
                        >
                          <rect x="1" y="3" width="15" height="13" />
                          <polygon points="16,8 20,8 23,11 23,16 16,16" />
                          <circle cx="5.5" cy="18.5" r="2.5" />
                          <circle cx="18.5" cy="18.5" r="2.5" />
                        </svg>
                      </div>
                    </div>
                    <div style={{ flex: 1 }}>
                      <h3
                        style={{
                          fontSize: '16px',
                          fontWeight: 600,
                          color: theme.primaryText,
                          margin: '0 0 4px 0',
                        }}
                      >
                        Connect your bank account
                      </h3>
                      <p
                        style={{
                          fontSize: '14px',
                          color: theme.secondaryText,
                          margin: '0 0 12px 0',
                        }}
                      >
                        Import bank statements to enable automatic
                        reconciliation
                      </p>
                      <button
                        onClick={handleNavigateToLedger}
                        style={{
                          ...getButtonStyle(theme, 'secondary'),
                          fontSize: '12px',
                          padding: '6px 12px',
                        }}
                      >
                        Connect Bank
                      </button>
                    </div>
                  </div>

                  {/* Configure VAT Step */}
                  <div style={{ display: 'flex', gap: '16px' }}>
                    <div style={{ flexShrink: 0 }}>
                      <div
                        style={{
                          width: '40px',
                          height: '40px',
                          backgroundColor: theme.surface,
                          border: `1px solid ${theme.border}`,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          style={{ opacity: 0.6 }}
                        >
                          <circle cx="12" cy="12" r="3" />
                          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
                        </svg>
                      </div>
                    </div>
                    <div style={{ flex: 1 }}>
                      <h3
                        style={{
                          fontSize: '16px',
                          fontWeight: 600,
                          color: theme.primaryText,
                          margin: '0 0 4px 0',
                        }}
                      >
                        Configure VAT settings
                      </h3>
                      <p
                        style={{
                          fontSize: '14px',
                          color: theme.secondaryText,
                          margin: '0 0 12px 0',
                        }}
                      >
                        Set up VAT rates and preferences to track your position
                      </p>
                      <button
                        onClick={handleNavigateToVAT}
                        style={{
                          ...getButtonStyle(theme, 'secondary'),
                          fontSize: '12px',
                          padding: '6px 12px',
                        }}
                      >
                        Setup VAT
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity Section */}
            <div style={getCardStyle(theme)}>
              <div
                style={{
                  padding: '24px',
                  borderBottom: `1px solid ${theme.border}`,
                }}
              >
                <h2
                  style={{
                    fontSize: '20px',
                    fontWeight: 600,
                    color: theme.primaryText,
                    margin: 0,
                  }}
                >
                  Recent Activity
                </h2>
              </div>

              <div style={{ padding: '24px' }}>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '200px',
                    textAlign: 'center',
                  }}
                >
                  <div style={{ flexShrink: 0 }}>
                    <div
                      style={{
                        width: '48px',
                        height: '48px',
                        backgroundColor: theme.surface,
                        border: `1px solid ${theme.border}`,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto 16px auto',
                      }}
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        style={{ opacity: 0.6 }}
                      >
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="12,6 12,12 16,14" />
                      </svg>
                    </div>
                  </div>
                  <p
                    style={{
                      fontSize: '16px',
                      fontWeight: 500,
                      color: theme.primaryText,
                      margin: '0 0 8px 0',
                    }}
                  >
                    No recent activity to display
                  </p>
                  <p
                    style={{
                      fontSize: '14px',
                      color: theme.secondaryText,
                      margin: 0,
                    }}
                  >
                    Activity will appear here once you start using the
                    application
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* AI Automation Sidebar */}
        <aside style={getCardStyle(theme)}>
          <AutomationHelpers
            onToggle={(helperId, enabled) => {
              console.log('Automation helper toggled:', helperId, enabled)
              // TODO: Implement actual toggle logic for automation features
            }}
          />
        </aside>
      </div>
    </div>
  )
}
