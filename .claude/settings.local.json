{"permissions": {"allow": ["Read(//Users/<USER>/Documents/Coding/wt/track-auth/apps/web/**)", "Read(//Users/<USER>/Documents/Coding/wt/track-auth/apps/web/**)", "Read(//Users/<USER>/Documents/Coding/wt/track-auth/packages/dal/src/**)", "Read(//Users/<USER>/Documents/Coding/wt/track-auth/packages/db/migrations/**)", "Bash(grep:*)", "Bash(npm test:*)", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "Bash(pnpm -w typecheck)", "Bash(pnpm --filter @ledgerly/types lint)", "Bash(pnpm -w test)", "Bash(python -m pytest tests/ -v)", "Bash(pnpm --filter @ledgerly/dal test)", "Bash(pnpm typecheck:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(uv run ruff:*)", "Bash(PYTHONPATH=src uv run pytest tests/ -v)", "Bash(PYTHONPATH=src uv run pytest tests/ -v --tb=short)", "Bash(pnpm add:*)", "Bash(pnpm lint:*)", "Bash(npx eslint:*)", "Bash(vercel unlink:*)", "Bash(vercel remove:*)", "<PERSON><PERSON>(vercel:*)", "Bash(git ls-tree:*)", "<PERSON><PERSON>(source:*)", "Bash(psql:*)", "Bash(git worktree add:*)", "Read(/Users/<USER>/Documents/Coding/wt/track-b-ai-extract/**)", "Read(/Users/<USER>/Documents/Coding/wt/track-b-ai-extract/**)", "Read(/Users/<USER>/Documents/Coding/wt/track-b-ai-extract/**)", "Read(/Users/<USER>/Documents/Coding/wt/track-b-ai-extract/**)", "Read(/Users/<USER>/Documents/Coding/wt/track-b-ai-extract/**)", "Bash(pnpm -w lint)", "Bash(pnpm --filter @ledgerly/domain-bank test)", "Bash(pnpm --filter @ledgerly/bff typecheck)", "Bash(pnpm --filter @ledgerly/import-service build)", "Bash(pnpm --filter @ledgerly/domain-bank build)", "Bash(pnpm -w build)", "Bash(pnpm --filter @ledgerly/web build)", "<PERSON><PERSON>(uv run:*)", "Bash(uv sync:*)", "Bash(pnpm --filter @ledgerly/web test __tests__/security/)", "Bash(pnpm --filter @ledgerly/bff lint --format=compact)", "Bash(pnpm --filter @ledgerly/dal lint)", "Bash(pnpm --filter @ledgerly/dal build)", "Bash(pnpm --filter @ledgerly/bff lint)", "Bash(pnpm --filter @ledgerly/web lint --format=compact)", "Bash(pnpm:*)", "Read(//Users/<USER>/Documents/Coding/wt/track-b-ai-extract/**)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/Documents/Coding/wt/track-auth/apps/web", "/Users/<USER>/Documents/Coding/wt/track-auth", "/Users/<USER>/Documents/Coding/wt/track-inbox-e2e", "/Users/<USER>/Documents/Coding/wt/wt"]}}