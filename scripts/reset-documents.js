#!/usr/bin/env node

/**
 * Document Processing Reset Script
 * 
 * This script resets all documents to "unprocessed" state and cleans extracted data
 * from the database. It can be used for both local development and staging environments.
 * 
 * Usage:
 *   npm run reset-docs              # Reset local database
 *   npm run reset-docs -- --staging # Reset staging database
 *   npm run reset-docs -- --prod    # Reset production database (requires confirmation)
 * 
 * What it does:
 * 1. Resets inbox_documents status to 'uploaded' (unprocessed)
 * 2. Clears all extracted data (invoices, journals, journal_lines)
 * 3. Cleans processing results and task status
 * 4. Clears file processing cache
 * 5. Removes document embeddings
 * 6. Preserves original document files and metadata
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config({ path: '.env.local' });

const ENVIRONMENTS = {
  local: {
    url: 'http://127.0.0.1:54321',
    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
  },
  staging: {
    url: process.env.SUPABASE_URL,
    key: process.env.SUPABASE_SERVICE_ROLE_KEY
  },
  prod: {
    url: process.env.PRODUCTION_SUPABASE_URL || process.env.PROD_SUPABASE_URL,
    key: process.env.PRODUCTION_SUPABASE_SERVICE_ROLE_KEY || process.env.PROD_SUPABASE_SERVICE_ROLE_KEY
  }
};

async function resetDocuments(environment = 'local') {
  console.log(`🔄 Starting document reset for ${environment} environment...`);
  
  const env = ENVIRONMENTS[environment];
  if (!env.url || !env.key) {
    throw new Error(`Missing configuration for ${environment} environment`);
  }

  const supabase = createClient(env.url, env.key);

  // Production safety check
  if (environment === 'prod') {
    console.log('⚠️  WARNING: You are about to reset PRODUCTION data!');
    console.log('This will permanently delete all extracted data.');
    console.log('Type "CONFIRM PRODUCTION RESET" to continue:');
    
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const confirmation = await new Promise(resolve => {
      readline.question('> ', resolve);
    });
    readline.close();
    
    if (confirmation !== 'CONFIRM PRODUCTION RESET') {
      console.log('❌ Production reset cancelled');
      return;
    }
  }

  const resetOperations = [
    {
      name: 'Reset inbox documents to uploaded status',
      query: `
        UPDATE inbox_documents 
        SET 
          status = 'uploaded',
          extraction = NULL,
          confidence = NULL,
          suggestion = NULL,
          posted_journal_id = NULL,
          export_ref = NULL,
          error_msg = NULL,
          confirmed_at = NULL,
          confirmed_by = NULL,
          updated_at = NOW()
        WHERE status != 'uploaded'
      `
    },
    {
      name: 'Clear document processing results',
      query: 'DELETE FROM document_processing_results'
    },
    {
      name: 'Clear task status',
      query: 'DELETE FROM task_status'
    },
    {
      name: 'Clear file processing cache',
      query: 'DELETE FROM file_processing_cache'
    },
    {
      name: 'Clear invoice embeddings',
      query: 'DELETE FROM invoice_embeddings'
    },
    {
      name: 'Clear journal lines',
      query: 'DELETE FROM journal_lines'
    },
    {
      name: 'Clear journals',
      query: 'DELETE FROM journals'
    },
    {
      name: 'Clear invoices',
      query: 'DELETE FROM invoices'
    },
    {
      name: 'Reset documents table associations',
      query: `
        UPDATE documents 
        SET 
          invoice_id = NULL,
          journal_id = NULL,
          metadata = COALESCE(metadata, '{}')
        WHERE invoice_id IS NOT NULL OR journal_id IS NOT NULL
      `
    }
  ];

  let totalAffected = 0;
  const results = [];

  for (const operation of resetOperations) {
    try {
      console.log(`📝 ${operation.name}...`);
      
      const { data, error, count } = await supabase
        .rpc('execute_sql', { sql: operation.query });
      
      if (error) {
        // Fallback to direct query execution
        const response = await fetch(`${env.url}/rest/v1/rpc/execute_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${env.key}`,
            'apikey': env.key
          },
          body: JSON.stringify({ sql: operation.query })
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
      }
      
      const affected = count || 0;
      totalAffected += affected;
      results.push({ operation: operation.name, affected });
      
      console.log(`   ✅ ${affected} rows affected`);
      
    } catch (error) {
      console.error(`   ❌ Failed: ${error.message}`);
      results.push({ operation: operation.name, error: error.message });
    }
  }

  // Summary
  console.log('\n📊 Reset Summary:');
  console.log('='.repeat(50));
  
  results.forEach(result => {
    if (result.error) {
      console.log(`❌ ${result.operation}: ${result.error}`);
    } else {
      console.log(`✅ ${result.operation}: ${result.affected} rows`);
    }
  });
  
  console.log('='.repeat(50));
  console.log(`🎉 Reset complete! Total rows affected: ${totalAffected}`);
  console.log(`📅 Reset performed at: ${new Date().toISOString()}`);
  
  if (environment === 'local') {
    console.log('\n💡 Your local database is now clean and ready for testing!');
    console.log('   You can upload documents through the UI to test processing.');
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
let environment = 'local';

if (args.includes('--staging')) {
  environment = 'staging';
} else if (args.includes('--prod') || args.includes('--production')) {
  environment = 'prod';
} else if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Document Processing Reset Script

Usage:
  npm run reset-docs              # Reset local database
  npm run reset-docs -- --staging # Reset staging database  
  npm run reset-docs -- --prod    # Reset production database

Options:
  --staging    Reset staging environment
  --prod       Reset production environment (requires confirmation)
  --help, -h   Show this help message

What it does:
• Resets inbox_documents status to 'uploaded' (unprocessed)
• Clears all extracted data (invoices, journals, journal_lines)
• Cleans processing results and task status
• Clears file processing cache and embeddings
• Preserves original document files and metadata
`);
  process.exit(0);
}

// Run the reset
resetDocuments(environment)
  .then(() => {
    console.log('\n✨ All done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 Reset failed:', error.message);
    process.exit(1);
  });
