'use client'

/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-return */

import { useState, useCallback } from 'react'
import { useCSRF } from './useCSRF'
import { parseJsonSafe, getApiErrorMessage } from '@/lib/http/api'

interface InvitationData {
  scope: 'tenant' | 'entity'
  scopeId: number
  email: string
  role: string
}

export function useInvitations() {
  const { addCSRFHeaders } = useCSRF()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const sendInvitation = useCallback(
    async (data: InvitationData) => {
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch('/api/invites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...addCSRFHeaders(),
          },
          body: JSON.stringify(data),
        })

        const result = await parseJsonSafe<{
          error?: string
          success?: boolean
        }>(response)

        if (!response.ok) {
          throw new Error(
            getApiErrorMessage(result, 'Failed to send invitation')
          )
        }

        return result
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to send invitation'
        setError(errorMessage)
        throw new Error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    [addCSRFHeaders]
  )

  const acceptInvitation = useCallback(
    async (token: string) => {
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch('/api/invites/accept', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...addCSRFHeaders(),
          },
          body: JSON.stringify({ token }),
        })

        const result = await parseJsonSafe<{
          error?: string
          success?: boolean
        }>(response)

        if (!response.ok) {
          throw new Error(
            getApiErrorMessage(result, 'Failed to accept invitation')
          )
        }

        return result
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to accept invitation'
        setError(errorMessage)
        throw new Error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    [addCSRFHeaders]
  )

  const cancelInvitation = useCallback(
    async (token: string) => {
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/invites/${token}`, {
          method: 'DELETE',
          headers: addCSRFHeaders(),
        })

        const result = await parseJsonSafe<{
          error?: string
          success?: boolean
        }>(response)

        if (!response.ok) {
          throw new Error(
            getApiErrorMessage(result, 'Failed to cancel invitation')
          )
        }

        return result
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to cancel invitation'
        setError(errorMessage)
        throw new Error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    [addCSRFHeaders]
  )

  const getInvitationDetails = useCallback(async (token: string) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/invites/${token}`)
      const result = await parseJsonSafe<
        { error?: string } & Record<string, unknown>
      >(response)

      if (!response.ok) {
        throw new Error(
          getApiErrorMessage(result, 'Failed to get invitation details')
        )
      }

      return result
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to get invitation details'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    sendInvitation,
    acceptInvitation,
    cancelInvitation,
    getInvitationDetails,
    isLoading,
    error,
    clearError: () => setError(null),
  }
}
