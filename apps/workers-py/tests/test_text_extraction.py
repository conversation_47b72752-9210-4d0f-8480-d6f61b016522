"""Tests for layout-aware TextExtractionService."""

from __future__ import annotations

from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from workers_py.extraction.models import <PERSON>crLine, OcrResult, OcrToken, BoundingBox
from workers_py.services.text_extraction import TextExtractionError, TextExtractionService


def _simple_ocr_result(text: str) -> OcrResult:
    line = OcrLine(
        id="p1_l1",
        text=text,
        bbox=BoundingBox(0.0, 0.0, 1.0, 0.1),
        page=1,
        token_ids=["p1_t1"],
    )
    token = OcrToken(
        id="p1_t1",
        text=text,
        bbox=BoundingBox(0.0, 0.0, 1.0, 0.1),
        confidence=0.9,
        page=1,
        line_id="p1_l1",
    )
    return OcrResult(text=text, tokens=[token], lines=[line], pages=[])


@pytest.fixture
def service() -> TextExtractionService:
    return TextExtractionService()


@pytest.mark.asyncio
async def test_extract_bytes_empty(service):
    with pytest.raises(TextExtractionError, match="Empty file bytes provided"):
        await service.extract_text_from_bytes(b"")


@pytest.mark.asyncio
async def test_extract_pdf_success(service):
    with patch.object(service, "_ocr_pdf", new_callable=AsyncMock) as mock_pdf:
        mock_pdf.return_value = _simple_ocr_result("Hello")
        result, mime = await service.extract_text_from_bytes(b"fake", "application/pdf")
        assert result.text == "Hello"
        assert mime == "application/pdf"


@pytest.mark.asyncio
async def test_extract_image_success(service):
    with patch.object(service, "_ocr_single_image", new_callable=AsyncMock) as mock_img:
        mock_img.return_value = _simple_ocr_result("Hello")
        result, mime = await service.extract_text_from_bytes(b"fake", "image/png")
        assert result.text == "Hello"
        assert mime == "image/png"


@pytest.mark.asyncio
async def test_extract_unsupported_mime(service):
    with pytest.raises(TextExtractionError, match="Unsupported MIME type"):
        await service.extract_text_from_bytes(b"fake", "application/zip")


@pytest.mark.asyncio
async def test_extract_empty_text(service):
    with patch.object(service, "_ocr_pdf", new_callable=AsyncMock) as mock_pdf:
        mock_pdf.return_value = _simple_ocr_result("   ")
        with pytest.raises(TextExtractionError, match="OCR produced empty text"):
            await service.extract_text_from_bytes(b"fake", "application/pdf")


@pytest.mark.asyncio
async def test_ocr_pdf_error(service):
    with patch("pdf2image.convert_from_path", side_effect=Exception("boom")):
        with pytest.raises(TextExtractionError, match="PDF conversion failed"):
            await service._ocr_pdf(Path("/tmp/fake.pdf"))


@pytest.mark.asyncio
async def test_ocr_pdf_success(service):
    dummy_image = MagicMock()
    with patch("pdf2image.convert_from_path", return_value=[dummy_image]):
        with patch.object(service, "_ocr_images", new_callable=AsyncMock) as mock_ocr:
            expected = _simple_ocr_result("pdf text")
            mock_ocr.return_value = expected
            result = await service._ocr_pdf(Path("/tmp/fake.pdf"))
            assert result == expected


@pytest.mark.asyncio
async def test_ocr_single_image(service):
    with patch("PIL.Image.open", return_value=MagicMock()) as mock_open:
        with patch.object(service, "_ocr_images", new_callable=AsyncMock) as mock_ocr:
            expected = _simple_ocr_result("img text")
            mock_ocr.return_value = expected
            result = await service._ocr_single_image(Path("/tmp/fake.png"))
            mock_open.assert_called_once()
            assert result == expected


@pytest.mark.asyncio
async def test_parse_ocr_data_builds_tokens(service):
    data = {
        "text": ["ABC"],
        "page_num": [1],
        "level": [5],
        "block_num": [1],
        "par_num": [1],
        "line_num": [1],
        "left": [10],
        "top": [20],
        "width": [50],
        "height": [10],
        "conf": [90],
    }

    tokens, lines, texts = service._parse_ocr_data(data, page_number=1, width=100, height=100)
    assert len(tokens) == 1
    assert len(lines) == 1
    assert texts == ["ABC"]


@pytest.mark.asyncio
async def test_detect_mime_failure_logs(service, caplog):
    with patch.object(service.mime, "from_buffer", side_effect=Exception("boom")):
        mime = service._detect_mime_type(b"fake")
        assert mime == "application/octet-stream"
        assert "MIME type detection failed" in caplog.text
