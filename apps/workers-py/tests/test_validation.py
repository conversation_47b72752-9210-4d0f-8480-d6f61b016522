"""Tests for extraction validation."""

import pytest
from decimal import Decimal

from workers_py.validation import (
    ExtractionValidator, ValidationResult, ValidationError,
    validate_extraction_result
)
from workers_py.models import (
    ExtractionResult,
    SupplierInfo,
    CustomerInfo,
    InvoiceInfo,
    TotalsInfo,
    PaymentInstructions,
    LineItem,
    VatRate,
)


class TestValidationResult:
    """Test ValidationResult class."""

    def test_initialization(self):
        """Test ValidationResult initialization."""
        result = ValidationResult()
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert result.final_confidence_multiplier == 1.0

    def test_add_error(self):
        """Test adding validation errors."""
        result = ValidationResult()
        result.add_error("Test error", "field", "CODE")
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert result.errors[0].message == "Test error"
        assert result.errors[0].field == "field"
        assert result.errors[0].code == "CODE"

    def test_add_warning(self):
        """Test adding validation warnings."""
        result = ValidationResult()
        result.add_warning("Test warning")
        
        assert result.is_valid is True  # Warnings don't invalidate
        assert len(result.warnings) == 1
        assert result.warnings[0] == "Test warning"

    def test_adjust_confidence(self):
        """Test confidence adjustment."""
        result = ValidationResult()
        result.adjust_confidence("Test reason", 0.9)
        result.adjust_confidence("Another reason", 0.8)
        
        assert len(result.confidence_adjustments) == 2
        assert result.final_confidence_multiplier == pytest.approx(0.72)  # 0.9 * 0.8

    def test_to_dict(self):
        """Test conversion to dictionary."""
        result = ValidationResult()
        result.add_error("Error message", "field", "CODE")
        result.add_warning("Warning message")
        result.adjust_confidence("Low confidence", 0.8)
        
        dict_result = result.to_dict()
        
        assert dict_result["is_valid"] is False
        assert len(dict_result["errors"]) == 1
        assert len(dict_result["warnings"]) == 1
        assert dict_result["final_confidence_multiplier"] == 0.8


class TestExtractionValidator:
    """Test ExtractionValidator class."""

    @pytest.fixture
    def validator(self):
        """Create validator instance."""
        return ExtractionValidator()

    @pytest.fixture
    def valid_extraction(self):
        """Create valid extraction result for testing."""
        return ExtractionResult(
            supplier=SupplierInfo(
                name="Test Company BVBA",
                vat="BE0123456789",
                iban="BE68 5390 0754 7034",
            ),
            customer=CustomerInfo(name="Client NV", vat="BE1111111111"),
            invoice=InvoiceInfo(
                number="INV-2024-001",
                issue_date="2024-08-15",
                due_date="2024-09-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00",
            ),
            totals=TotalsInfo(net="100.00", vat="21.00", gross="121.00", currency="EUR"),
            payment_instructions=PaymentInstructions(iban="BE68 5390 0754 7034", bic="BBRUBEBB"),
            lines=[
                LineItem(
                    description="Consulting services",
                    quantity="1",
                    unit_price="100.00",
                    vat_rate=VatRate.STANDARD,
                )
            ],
        )

    def test_validate_valid_extraction(self, validator, valid_extraction):
        """Test validation of valid extraction."""
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert final_confidence == 0.9  # No adjustments for valid extraction

    def test_missing_supplier_name(self, validator, valid_extraction):
        """Test validation with missing supplier name."""
        valid_extraction.supplier.name = ""
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "MISSING_SUPPLIER_NAME" for error in result.errors)

    def test_invalid_vat_format(self, validator, valid_extraction):
        """Test validation with invalid VAT format."""
        valid_extraction.supplier.vat = "INVALID_VAT"
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is True  # Warning, not error
        assert len(result.warnings) > 0
        assert final_confidence < 0.9  # Should be reduced

    def test_missing_invoice_number(self, validator, valid_extraction):
        """Test validation with missing invoice number."""
        valid_extraction.invoice.number = ""
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "MISSING_INVOICE_NUMBER" for error in result.errors)

    def test_invalid_date_format(self, validator, valid_extraction):
        """Test validation with invalid date format."""
        valid_extraction.invoice.issue_date = "15-08-2024"  # Wrong format
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "INVALID_DATE_FORMAT" for error in result.errors)

    def test_negative_amounts(self, validator, valid_extraction):
        """Test validation with negative amounts."""
        valid_extraction.invoice.net = "-100.00"
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "NEGATIVE_AMOUNT" for error in result.errors)

    def test_arithmetic_inconsistency(self, validator, valid_extraction):
        """Test validation with arithmetic inconsistency."""
        valid_extraction.invoice.gross = "130.00"  # Should be 121.00
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "ARITHMETIC_MISMATCH" for error in result.errors)

    def test_minor_arithmetic_inconsistency(self, validator, valid_extraction):
        """Test validation with minor arithmetic inconsistency."""
        valid_extraction.invoice.gross = "121.01"  # 1 cent off
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is True  # Warning, not error for small differences
        assert len(result.warnings) > 0
        assert final_confidence < 0.9

    def test_missing_line_items(self, validator, valid_extraction):
        """Test validation with no line items."""
        valid_extraction.lines = []
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "MISSING_LINE_ITEMS" for error in result.errors)

    def test_invalid_line_description(self, validator, valid_extraction):
        """Test validation with missing line description."""
        valid_extraction.lines[0].description = ""
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "MISSING_LINE_DESCRIPTION" for error in result.errors)

    def test_invalid_vat_rate(self, validator, valid_extraction):
        """Test validation with invalid VAT rate."""
        # Create line with invalid VAT rate
        class InvalidVatRate:
            value = 15  # Invalid rate
        
        valid_extraction.lines[0].vat_rate = InvalidVatRate()
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "INVALID_VAT_RATE" for error in result.errors)

    def test_invalid_date_order(self, validator, valid_extraction):
        """Test validation with due date before issue date."""
        valid_extraction.invoice.issue_date = "2024-09-15"
        valid_extraction.invoice.due_date = "2024-08-15"  # Before issue date
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "INVALID_DATE_ORDER" for error in result.errors)

    def test_non_eur_currency_warning(self, validator, valid_extraction):
        """Test validation with non-EUR currency."""
        valid_extraction.invoice.currency = "USD"
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is True  # Warning, not error
        assert len(result.warnings) > 0
        assert final_confidence < 0.9

    def test_zero_amount_warning(self, validator, valid_extraction):
        """Test validation with zero amounts."""
        valid_extraction.invoice.net = "0.00"
        valid_extraction.invoice.vat = "0.00" 
        valid_extraction.invoice.gross = "0.00"
        valid_extraction.lines[0].unit_price = "0.00"
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is True  # Warning, not error for zero amounts
        assert len(result.warnings) > 0
        assert final_confidence < 0.9

    def test_high_amount_warning(self, validator, valid_extraction):
        """Test validation with very high amounts."""
        valid_extraction.invoice.net = "120000.00"
        valid_extraction.invoice.vat = "30000.00"
        valid_extraction.invoice.gross = "150000.00"  # €150k
        valid_extraction.totals.net = "120000.00"
        valid_extraction.totals.vat = "30000.00"
        valid_extraction.totals.gross = "150000.00"
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is True  # Warning, not error
        assert len(result.warnings) > 0
        assert final_confidence < 0.9

    def test_confidence_clamping(self, validator, valid_extraction):
        """Test that confidence is clamped to [0,1] range."""
        # Apply multiple severe adjustments
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.1)
        
        # Apply manual severe adjustments
        result.adjust_confidence("Test", 0.1)
        result.adjust_confidence("Test", 0.1)
        
        # Recalculate confidence manually
        adjusted = 0.1 * result.final_confidence_multiplier
        clamped = max(0.0, min(1.0, adjusted))
        
        assert 0.0 <= clamped <= 1.0

    def test_invalid_amount_format(self, validator, valid_extraction):
        """Test validation with invalid amount formats."""
        valid_extraction.invoice.net = "not_a_number"
        
        result, final_confidence = validator.validate_extraction(valid_extraction, 0.9)
        
        assert result.is_valid is False
        assert any(error.code == "INVALID_AMOUNT_FORMAT" for error in result.errors)


class TestValidationConvenience:
    """Test convenience functions."""

    def test_validate_extraction_result_function(self):
        """Test the convenience validation function."""
        extraction = ExtractionResult(
            supplier=SupplierInfo(name="Test Company"),
            invoice=InvoiceInfo(
                number="INV-001",
                issue_date="2024-08-15",
                currency="EUR",
                net="100.00",
                vat="21.00",
                gross="121.00"
            ),
            lines=[
                LineItem(
                    description="Test item",
                    quantity="1",
                    unit_price="100.00",
                    vat_rate=VatRate.STANDARD
                )
            ]
        )
        
        result, confidence = validate_extraction_result(extraction, 0.85)
        
        assert isinstance(result, ValidationResult)
        assert isinstance(confidence, float)
        assert 0.0 <= confidence <= 1.0
