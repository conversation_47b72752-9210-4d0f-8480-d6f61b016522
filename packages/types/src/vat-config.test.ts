// VAT Configuration Tests
import { describe, it, expect } from 'vitest'
import {
  VATSetupRequestSchema,
  isVATEnabled,
  extractVATConfiguration,
  createOperatingModeConfigWithVAT,
  validateBelgianVATNumber,
} from './vat-config'

describe('VAT Configuration Types', () => {
  describe('VATSetupRequestSchema', () => {
    it('should validate a valid VAT setup request', () => {
      const validRequest = {
        enabled: true,
        vatNumber: 'BE0123456789',
        filingFrequency: 'quarterly' as const,
        defaultRates: {
          standard: 0.21,
          reduced1: 0.12,
          reduced2: 0.06,
          zero: 0.00,
        },
      }

      const result = VATSetupRequestSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
    })

    it('should reject invalid filing frequency', () => {
      const invalidRequest = {
        enabled: true,
        filingFrequency: 'yearly', // Invalid
        defaultRates: {
          standard: 0.21,
          reduced1: 0.12,
          reduced2: 0.06,
          zero: 0.00,
        },
      }

      const result = VATSetupRequestSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
    })

    it('should reject invalid VAT rates', () => {
      const invalidRequest = {
        enabled: true,
        filingFrequency: 'quarterly' as const,
        defaultRates: {
          standard: 1.5, // Invalid - over 100%
          reduced1: 0.12,
          reduced2: 0.06,
          zero: 0.00,
        },
      }

      const result = VATSetupRequestSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
    })
  })

  describe('isVATEnabled', () => {
    it('should return true for new structured config', () => {
      const config = {
        vat: {
          enabled: true,
          filingFrequency: 'quarterly',
          defaultRates: {
            standard: 0.21,
            reduced1: 0.12,
            reduced2: 0.06,
            zero: 0.00,
          },
          country: 'BE',
        },
      }

      expect(isVATEnabled(config)).toBe(true)
    })

    it('should return true for legacy config', () => {
      const config = {
        VATEnabled: true,
      }

      expect(isVATEnabled(config)).toBe(true)
    })

    it('should return false for disabled VAT', () => {
      const config = {
        vat: {
          enabled: false,
          filingFrequency: 'quarterly',
          defaultRates: {
            standard: 0.21,
            reduced1: 0.12,
            reduced2: 0.06,
            zero: 0.00,
          },
          country: 'BE',
        },
      }

      expect(isVATEnabled(config)).toBe(false)
    })

    it('should return false for invalid config', () => {
      expect(isVATEnabled(null)).toBe(false)
      expect(isVATEnabled(undefined)).toBe(false)
      expect(isVATEnabled('invalid')).toBe(false)
    })
  })

  describe('extractVATConfiguration', () => {
    it('should extract structured VAT configuration', () => {
      const config = {
        vat: {
          enabled: true,
          vatNumber: 'BE0123456789',
          filingFrequency: 'quarterly',
          defaultRates: {
            standard: 0.21,
            reduced1: 0.12,
            reduced2: 0.06,
            zero: 0.00,
          },
          country: 'BE',
        },
      }

      const result = extractVATConfiguration(config)
      expect(result).toEqual(config.vat)
    })

    it('should create minimal config from legacy flag', () => {
      const config = {
        VATEnabled: true,
      }

      const result = extractVATConfiguration(config)
      expect(result).toEqual({
        enabled: true,
        filingFrequency: 'quarterly',
        defaultRates: {
          standard: 0.21,
          reduced1: 0.12,
          reduced2: 0.06,
          zero: 0.00,
        },
        country: 'BE',
      })
    })

    it('should return null for disabled VAT', () => {
      const config = {
        VATEnabled: false,
      }

      expect(extractVATConfiguration(config)).toBe(null)
    })
  })

  describe('createOperatingModeConfigWithVAT', () => {
    it('should merge VAT config with existing config', () => {
      const existingConfig = {
        someOtherSetting: 'value',
      }

      const vatConfig = {
        enabled: true,
        filingFrequency: 'quarterly' as const,
        defaultRates: {
          standard: 0.21,
          reduced1: 0.12,
          reduced2: 0.06,
          zero: 0.00,
        },
        country: 'BE',
      }

      const result = createOperatingModeConfigWithVAT(existingConfig, vatConfig)

      expect(result).toEqual({
        someOtherSetting: 'value',
        VATEnabled: true,
        vat: vatConfig,
      })
    })
  })

  describe('validateBelgianVATNumber', () => {
    it('should validate correct Belgian VAT numbers', () => {
      expect(validateBelgianVATNumber('BE0123456789')).toBe(true)
      expect(validateBelgianVATNumber('BE9876543210')).toBe(true)
    })

    it('should reject invalid Belgian VAT numbers', () => {
      expect(validateBelgianVATNumber('BE123456789')).toBe(false) // Too short
      expect(validateBelgianVATNumber('BE01234567890')).toBe(false) // Too long
      expect(validateBelgianVATNumber('FR0123456789')).toBe(false) // Wrong country
      expect(validateBelgianVATNumber('BE012345678A')).toBe(false) // Contains letter
      expect(validateBelgianVATNumber('0123456789')).toBe(false) // Missing BE prefix
    })
  })
})