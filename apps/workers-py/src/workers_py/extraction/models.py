"""Data structures for layout-agnostic invoice extraction pipeline."""

from __future__ import annotations

import base64
from dataclasses import dataclass, field


@dataclass
class BoundingBox:
    """Axis-aligned bounding box expressed in relative page coordinates."""

    x0: float
    y0: float
    x1: float
    y1: float

    def padded(self, pad: float = 0.01) -> BoundingBox:
        """Return bounding box expanded by padding while clamping to [0, 1]."""
        return BoundingBox(
            max(0.0, self.x0 - pad),
            max(0.0, self.y0 - pad),
            min(1.0, self.x1 + pad),
            min(1.0, self.y1 + pad),
        )


@dataclass
class DocumentPage:
    """Normalized view of a page produced by OCR."""

    number: int
    width: int
    height: int
    image_bytes: bytes
    preview_base64: str

    @classmethod
    def from_image(cls, number: int, image, max_preview_width: int = 1280) -> DocumentPage:
        """Create a DocumentPage from a PIL image."""
        from io import BytesIO

        if image.mode != "RGB":
            image = image.convert("RGB")

        width, height = image.size

        # Build high-resolution bytes for downstream cropping
        raw_buffer = BytesIO()
        image.save(raw_buffer, format="PNG")
        raw_bytes = raw_buffer.getvalue()

        # Generate preview scaled to max width to keep JSON payload manageable
        preview = image
        if width > max_preview_width:
            ratio = max_preview_width / float(width)
            preview = image.resize((int(width * ratio), int(height * ratio)))

        preview_buffer = BytesIO()
        preview.save(preview_buffer, format="JPEG", quality=85)
        preview_base64 = base64.b64encode(preview_buffer.getvalue()).decode("utf-8")

        return cls(
            number=number,
            width=width,
            height=height,
            image_bytes=raw_bytes,
            preview_base64=preview_base64,
        )


@dataclass
class OcrToken:
    """Token recognized by OCR with bounding box and confidence."""

    id: str
    text: str
    bbox: BoundingBox
    confidence: float
    page: int
    line_id: str


@dataclass
class OcrLine:
    """Line constructed from OCR tokens."""

    id: str
    text: str
    bbox: BoundingBox
    page: int
    token_ids: list[str] = field(default_factory=list)


@dataclass
class OcrResult:
    """Aggregate OCR output across pages."""

    text: str
    tokens: list[OcrToken]
    lines: list[OcrLine]
    pages: list[DocumentPage]

    def get_page(self, number: int) -> DocumentPage | None:
        """Return page metadata by number."""
        for page in self.pages:
            if page.number == number:
                return page
        return None


@dataclass
class Region:
    """Logical text region built from clustered OCR lines."""

    id: str
    page: int
    bbox: BoundingBox
    text: str
    line_ids: list[str]
    token_ids: list[str]
    image_bytes: bytes


@dataclass
class RegionClassification:
    """Classification result for a region."""

    region: Region
    label: str
    score: float
    probabilities: dict[str, float]
    evidence: list[str] = field(default_factory=list)
    extracted_fields: dict[str, dict[str, str]] = field(default_factory=dict)
    features: dict[str, float] = field(default_factory=dict)
    source: str = "heuristic"


@dataclass
class RegionCandidate:
    """Candidate selection for a particular downstream field."""

    field: str
    region_id: str
    label: str
    score: float
    evidence: list[str] = field(default_factory=list)
    data: dict[str, str] = field(default_factory=dict)



@dataclass
class DocumentContext:
    """Context passed to extraction adapters."""

    ocr: OcrResult
    mime_type: str
