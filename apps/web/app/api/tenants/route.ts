import { NextRequest } from 'next/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import type {
  TenantResponse,
  TenantsListResponse,
  ErrorResponse,
} from '@/lib/api-types'
import { isTenantRequest } from '@/lib/api-types'
import { createSecureServerClient } from '@/lib/supabase-server'

/**
 * Create a new tenant
 * This route requires CSRF protection as it performs state-changing operations
 */
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    // Parse and validate request body
    const body: unknown = await request.json()

    if (!isTenantRequest(body)) {
      return createJsonResponse<ErrorResponse>(
        { error: 'Invalid request body. Required field: name' },
        400
      )
    }

    const { name, org_type = 'sme' } = body

    // Use the RPC function to create tenant with proper org_type handling
    const { data: tenantId, error } = await supabase.rpc('rpc_create_tenant', {
      p_name: name.trim(),
      p_org_type: org_type,
    })

    if (error) {
      console.error('Error creating tenant:', error)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to create tenant' },
        500
      )
    }

    // Fetch the created tenant to return full details
    const { data: tenant, error: fetchError } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', tenantId)
      .single()

    if (fetchError || !tenant) {
      console.error('Error fetching created tenant:', fetchError)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to fetch created tenant' },
        500
      )
    }

    return createJsonResponse<TenantResponse>({
      tenant: {
        id: tenant.id,
        name: tenant.name,
        kind: tenant.org_type || 'sme', // Keep for backward compatibility
        org_type: (tenant.org_type || 'sme') as 'sme' | 'firm',
        created_at: tenant.created_at,
        updated_at: tenant.updated_at,
      }
    })
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}

/**
 * Get user's tenants
 * This is a safe method and doesn't require CSRF protection
 */
export async function GET(_request: NextRequest): Promise<Response> {
  try {
    const supabase: SupabaseClient<Database> = createSecureServerClient()

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse<ErrorResponse>({ error: 'Unauthorized' }, 401)
    }

    const { data: tenants, error } = await supabase
      .from('v_user_tenants')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching tenants:', error)
      return createJsonResponse<ErrorResponse>(
        { error: 'Failed to fetch tenants' },
        500
      )
    }

    const tenantsWithOrgType = tenants?.map(tenant => ({
      ...tenant,
      org_type: (tenant.org_type || 'sme') as 'sme' | 'firm'
    })) || []

    return createJsonResponse<TenantsListResponse>({ tenants: tenantsWithOrgType })
  } catch (error) {
    console.error('API error:', error)
    return createJsonResponse<ErrorResponse>(
      { error: 'Internal server error' },
      500
    )
  }
}
