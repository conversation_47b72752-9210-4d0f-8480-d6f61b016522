"""Reconciliation service to produce self-consistent line items deterministically.

Inputs:
- OCR text
- Initial ExtractionResult (from LLM or stub)

Outputs:
- New ExtractionResult where each line has qty, unit price, vat rate and
  line totals (net/vat/gross) filled; arithmetic verified; rounding normalized.
"""

from __future__ import annotations

from decimal import ROUND_HALF_UP, Decimal, InvalidOperation
from typing import List, Tuple

from ..extraction.models import DocumentContext
from ..config import settings
from ..adapters.gemini_langextract import GeminiLangExtractAdapter, ExtractionError as AdapterError
import json
from ..models import ExtractionResult, LineItem, TotalsInfo, PaymentInstructions, InvoiceInfo
from .table_parser import ParsedRow, parse_ocr_table_lines


def _d(s: str | None) -> Decimal:
    if not s:
        return Decimal("0.00")
    try:
        return Decimal(str(s))
    except (InvalidOperation, ValueError):
        return Decimal("0.00")


def _q2(x: Decimal) -> Decimal:
    return x.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)


def _normalize_line(line: LineItem) -> LineItem:
    """Ensure line has deterministic totals; prefer extracted totals when present and consistent."""
    qty = _d(line.quantity)
    unit = _d(line.unit_price)
    rate = Decimal(int(line.vat_rate.value)) / Decimal(100)

    computed_net = _q2(qty * unit)
    computed_vat = _q2(computed_net * rate)
    computed_gross = computed_net + computed_vat

    # Prefer provided totals if present and self-consistent within 1 cent
    ln = _d(line.line_net)
    lv = _d(line.line_vat)
    lg = _d(line.line_gross)

    # If we have line_net and line_gross, accept them; infer VAT when missing
    if ln and lg:
        inferred_vat = _q2(ln * rate)
        valid = abs((ln + (lv if lv else inferred_vat)) - lg) <= Decimal("0.01")
        if valid:
            final_net = ln
            final_vat = lv if lv else inferred_vat
            final_gross = lg
        else:
            final_net = computed_net
            final_vat = computed_vat
            final_gross = computed_gross
    else:
        final_net = computed_net
        final_vat = computed_vat
        final_gross = computed_gross

    line_dict = line.model_dump()
    line_dict.update(
        {
            "line_net": f"{final_net:.2f}",
            "line_vat": f"{final_vat:.2f}",
            "line_gross": f"{final_gross:.2f}",
            "quantity": f"{qty:.0f}" if qty == qty.to_integral_value() else f"{qty}",
            "unit_price": f"{unit:.2f}",
        }
    )
    return LineItem(**line_dict)


def _best_row_match(
    description: str,
    rows: List[ParsedRow],
    used: set[int],
    qty_hint: Decimal | None = None,
    unit_hint: Decimal | None = None,
) -> tuple[int | None, ParsedRow | None]:
    desc_tokens = set(t.lower() for t in description.split() if len(t) > 2)
    best_idx = None
    best_row = None
    best_score = -1
    for idx, r in enumerate(rows):
        if idx in used:
            continue
        r_tokens = set(t.lower() for t in r.description.split() if len(t) > 2)
        base = len(desc_tokens & r_tokens)
        if base == 0:
            continue
        # Add small preference if qty/unit are close
        bonus = 0
        try:
            if qty_hint is not None and r.quantity:
                if Decimal(r.quantity) == qty_hint:
                    bonus += 1
            if unit_hint is not None and r.unit_price:
                if Decimal(r.unit_price.replace(",", ".")) == unit_hint:
                    bonus += 1
        except Exception:
            pass
        score = base * 10 + bonus  # prioritize description match, then tie-breakers
        if score > best_score:
            best_idx, best_row, best_score = idx, r, score
    return (best_idx, best_row) if best_score >= 10 else (None, None)


def reconcile_extraction(document: DocumentContext, extraction: ExtractionResult) -> ExtractionResult:
    """Produce a reconciled ExtractionResult with filled and verified line totals.

    Steps:
    1) Parse OCR text for table-like rows.
    2) For each extracted line, merge missing fields from best-matching parsed row.
    3) Deterministically compute and set line totals; verify arithmetic.
    4) Ensure invoice totals exist; if missing, sum from lines.
    """
    parsed_rows = parse_ocr_table_lines(document.ocr.text or "")
    used: set[int] = set()

    new_lines: list[LineItem] = []
    for line in extraction.lines:
        idx, row = _best_row_match(
            line.description or "",
            parsed_rows,
            used,
            qty_hint=_d(line.quantity),
            unit_hint=_d(line.unit_price),
        ) if parsed_rows else (None, None)
        line_dict = line.model_dump()
        if row:
            if idx is not None:
                used.add(idx)
            line_dict["quantity"] = line_dict.get("quantity") or row.quantity or "1"
            line_dict["unit_price"] = line_dict.get("unit_price") or row.unit_price or "0.00"
            line_dict["line_net"] = line_dict.get("line_net") or row.line_net
            line_dict["line_vat"] = line_dict.get("line_vat") or row.line_vat
            line_dict["line_gross"] = line_dict.get("line_gross") or row.line_gross
            if not line_dict.get("vat_rate") and row.vat_rate:
                try:
                    from ..models import VatRate

                    v = int(round(float(str(row.vat_rate).replace(",", "."))))
                    if v in (0, 6, 12, 21):
                        line_dict["vat_rate"] = VatRate(v)
                except Exception:
                    pass
        normalized = _normalize_line(LineItem(**line_dict))
        new_lines.append(normalized)

    # Fill invoice/totals if missing, based on lines
    net_sum = _q2(sum(_d(li.line_net) for li in new_lines))
    vat_sum = _q2(sum(_d(li.line_vat) for li in new_lines))
    gross_sum = net_sum + vat_sum

    inv = extraction.invoice
    totals = extraction.totals or TotalsInfo()
    inv_dict = inv.model_dump()
    totals_dict = totals.model_dump()

    inv_dict["net"] = inv_dict.get("net") or f"{net_sum:.2f}"
    inv_dict["vat"] = inv_dict.get("vat") or f"{vat_sum:.2f}"
    inv_dict["gross"] = inv_dict.get("gross") or f"{gross_sum:.2f}"
    totals_dict["net"] = totals_dict.get("net") or inv_dict["net"]
    totals_dict["vat"] = totals_dict.get("vat") or inv_dict["vat"]
    totals_dict["gross"] = totals_dict.get("gross") or inv_dict["gross"]

    result_dict = extraction.model_dump()
    result_dict["lines"] = new_lines
    result_dict["invoice"] = inv.__class__(**inv_dict)
    result_dict["totals"] = totals.__class__(**totals_dict)
    reconciled = ExtractionResult(**result_dict)

    # Optional LLM reconciliation: provide OCR text, parsed rows, and current lines to improve completeness
    mode = getattr(settings, "AI_RECONCILIATION_MODE", "deterministic")
    if mode in {"llm", "llm+deterministic"}:
        try:
            adapter = GeminiLangExtractAdapter()
            prompt = _build_llm_reconcile_prompt(document, reconciled, parsed_rows)
            from google import generativeai as genai

            model = genai.GenerativeModel(model_name=adapter.model_id)
            response = model.generate_content(
                [{"role": "user", "parts": [{"text": prompt}]}],
                generation_config=genai.types.GenerationConfig(
                    temperature=0.05,
                    response_mime_type="application/json",
                ),
            )
            text = getattr(response, "text", None)
            if not text:
                # Try to read nested candidates
                for c in getattr(response, "candidates", []) or []:
                    for p in getattr(getattr(c, "content", None), "parts", []) or []:
                        if getattr(p, "text", None):
                            text = p.text
                            break
                    if text:
                        break
            if text:
                data = json.loads(text)
                reconciled = _merge_llm_reconciled_lines(reconciled, data)
        except Exception:
            # If LLM reconciliation fails or API key not present, keep deterministic result
            pass

    # Fill missing payment fields and dates from OCR as a robust fallback
    reconciled = _fill_payments_and_dates_from_ocr(document, reconciled)
    return reconciled


def _build_llm_reconcile_prompt(doc: DocumentContext, extraction: ExtractionResult, rows: list[ParsedRow]) -> str:
    base = {
        "ocr": doc.ocr.text[:5000],  # limit prompt size
        "extracted_lines": [
            {
                "description": l.description,
                "quantity": l.quantity,
                "unit_price": l.unit_price,
                "vat_rate": int(l.vat_rate.value),
                "lineNet": l.line_net,
                "lineVat": l.line_vat,
                "lineGross": l.line_gross,
            }
            for l in extraction.lines
        ],
        "parsed_rows": [r.__dict__ for r in rows],
    }
    return (
        "You are reconciling invoice line items. Combine parsed rows and current lines to fill"
        " missing quantities, unit prices, VAT rates, and line totals. Output JSON with 'lines'"
        " as an array of {description, quantity, unitPrice, vatRate, lineNet, lineVat, lineGross}."
        " Ensure arithmetic: lineNet ≈ quantity*unitPrice, lineVat ≈ lineNet*vatRate/100,"
        " lineGross ≈ lineNet+lineVat, rounded to 2 decimals.\n\nINPUT:\n" + json.dumps(base)
    )


def _merge_llm_reconciled_lines(extraction: ExtractionResult, data: dict) -> ExtractionResult:
    lines = data.get("lines") or []
    if not isinstance(lines, list) or not lines:
        return extraction
    new_lines: list[LineItem] = []
    from ..models import VatRate

    for it in lines:
        try:
            li = LineItem(
                description=str(it.get("description") or "").strip(),
                quantity=str(it.get("quantity") or "1"),
                unit_price=str(it.get("unitPrice") or "0.00"),
                vat_rate=VatRate(int(round(float(str(it.get("vatRate") or 21))))),
                line_net=str(it.get("lineNet") or "0.00"),
                line_vat=str(it.get("lineVat") or "0.00"),
                line_gross=str(it.get("lineGross") or "0.00"),
            )
            # Normalize after ingest to enforce rounding
            new_lines.append(_normalize_line(li))
        except Exception:
            continue

    if not new_lines:
        return extraction

    result = extraction.model_dump()
    result["lines"] = new_lines
    return ExtractionResult(**result)


def _fill_payments_and_dates_from_ocr(doc: DocumentContext, extraction: ExtractionResult) -> ExtractionResult:
    """Fallback parser to recover payment instructions and dates from OCR text when missing.

    - IBAN: European IBAN with BE normalization
    - BIC: 8 or 11 alphanumeric uppercase
    - Structured reference: +++...+++
    - Dates: Issue and due date in DD/MM/YYYY, DD-MM-YYYY or YYYY-MM-DD near keywords
    """
    import re

    text = doc.ocr.text or ""

    # IBAN
    iban_match = re.search(r"\b([A-Z]{2}[0-9]{2}(?:[\s]?[A-Z0-9]{4}){3,7})\b", text)
    bic_match = re.search(r"\b([A-Z]{4}[A-Z]{2}[A-Z0-9]{2}(?:[A-Z0-9]{3})?)\b", text)
    struct_match = re.search(r"\+\+\+\s*([0-9/\s]{10,})\s*\+\+\+", text)

    pay = extraction.payment_instructions
    pay_dict = pay.model_dump() if pay else {}
    if iban_match and not pay_dict.get("iban"):
        raw_iban = iban_match.group(1)
        # light normalization: keep spaces groups of 4 for BE
        cleaned = re.sub(r"\s+", " ", raw_iban.strip())
        pay_dict["iban"] = cleaned
    if bic_match and not pay_dict.get("bic"):
        pay_dict["bic"] = bic_match.group(1)
    if struct_match and not pay_dict.get("structured_ref"):
        digits = re.sub(r"\s+", "", struct_match.group(1))
        pay_dict["structured_ref"] = f"+++{digits}+++"

    # Dates near labels
    def _norm_date(s: str) -> str | None:
        s = s.strip()
        m = re.match(r"^(\d{4})[-/](\d{2})[-/](\d{2})$", s)
        if m:
            return f"{m.group(1)}-{m.group(2)}-{m.group(3)}"
        m = re.match(r"^(\d{1,2})[-/](\d{1,2})[-/](\d{4})$", s)
        if m:
            d, mth, y = m.groups()
            return f"{y}-{mth.zfill(2)}-{d.zfill(2)}"
        return None

    inv = extraction.invoice
    inv_dict = inv.model_dump()

    # Issue date (labels: date, datum, facture, factuur)
    if not inv_dict.get("issue_date"):
        m = re.search(r"(?:date|datum)[^\d]*(\d{1,2}[-/]\d{1,2}[-/]\d{4}|\d{4}[-/]\d{2}[-/]\d{2})", text, re.IGNORECASE)
        if m:
            nd = _norm_date(m.group(1))
            if nd:
                inv_dict["issue_date"] = nd

    # Due date (labels: due, vervaldatum, echeance)
    if not inv_dict.get("due_date"):
        m = re.search(r"(?:due|vervaldatum|echeance|échéance)[^\d]*(\d{1,2}[-/]\d{1,2}[-/]\d{4}|\d{4}[-/]\d{2}[-/]\d{2})", text, re.IGNORECASE)
        if m:
            nd = _norm_date(m.group(1))
            if nd:
                inv_dict["due_date"] = nd

    # Assemble
    result = extraction.model_dump()
    if pay_dict:
        result["payment_instructions"] = PaymentInstructions(**pay_dict)
    result["invoice"] = InvoiceInfo(**inv_dict)
    return ExtractionResult(**result)
