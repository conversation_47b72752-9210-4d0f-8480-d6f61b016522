import { describe, it, expect } from 'vitest'
import { camelize<PERSON>eys } from '../src/utils/casing'

describe('camelizeKeys', () => {
  it('converts shallow object keys', () => {
    const input = { journal_date: '2025-01-01', reference_number: 'INV-1' }
    const out = camelizeKeys(input) as Record<string, unknown>
    expect(out).toHaveProperty('journalDate', '2025-01-01')
    expect(out).toHaveProperty('referenceNumber', 'INV-1')
  })

  it('converts arrays of objects', () => {
    const input = [{ account_id: 1, vat_code_id: 2 }]
    const out = camelizeKeys(input) as Array<Record<string, unknown>>
    expect(out[0]).toHaveProperty('accountId', 1)
    expect(out[0]).toHaveProperty('vatCodeId', 2)
  })

  it('handles nested structures', () => {
    const input = {
      suggestion: { journal_date: '2025-01-01', lines: [{ account_id: 1 }] },
    }
    const out = camelize<PERSON>eys(input) as Record<string, any>
    expect(out.suggestion).toHaveProperty('journalDate', '2025-01-01')
    expect(out.suggestion.lines[0]).toHaveProperty('accountId', 1)
  })
})
