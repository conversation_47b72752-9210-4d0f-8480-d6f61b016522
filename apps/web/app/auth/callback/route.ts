import { NextResponse } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { sessionManager } from '@/lib/session-security'
import type { Session } from '@supabase/supabase-js'

// Handles Supabase OAuth callback (GET) and client-initiated cookie sync (POST)

export async function GET(request: Request) {
  try {
    const supabase = createSecureServerClient()

    // Exchange the auth code for a session and set cookies via @supabase/ssr
    // This also sets the sb-access-token / sb-refresh-token cookies using our cookie handlers
    const { data, error } = await supabase.auth.exchangeCodeForSession(
      request.url
    )

    if (error) {
      // On failure, send user back to login with a message
      const url = new URL('/login', request.url)
      url.searchParams.set('error', 'oauth_callback_failed')
      return NextResponse.redirect(url)
    }

    // Initialize basic session security metadata when possible
    try {
      const session = data.session
      if (session) {
        const token = session.access_token
        const userId = session.user?.id
        if (token && userId) {
          const existing = sessionManager.getSession(token)
          if (!existing) {
            await sessionManager.initializeSession(token, userId, {
              headers: request.headers,
            })
          } else {
            sessionManager.updateActivity(token, { headers: request.headers })
          }
        }
      }
    } catch (e) {
      console.warn('Auth callback: session manager init failed', e)
    }

    // Respect optional redirect target
    const redirectUrl = new URL(request.url)
    const target = redirectUrl.searchParams.get('redirectTo') || '/'
    return NextResponse.redirect(new URL(target, request.url))
  } catch (e) {
    console.error('Auth callback GET error:', e)
    const url = new URL('/login', request.url)
    url.searchParams.set('error', 'oauth_callback_error')
    return NextResponse.redirect(url)
  }
}

export async function POST(request: Request) {
  try {
    const supabase = createSecureServerClient()
    const raw: unknown = await request.json()
    if (typeof raw !== 'object' || raw === null || !('event' in raw)) {
      return NextResponse.json(
        { success: false, error: 'invalid_payload' },
        { status: 400 }
      )
    }
    const { event, session } = raw as { event: string; session: unknown }

    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      // Set/update SSR cookies for the current session
      if (session && typeof session === 'object') {
        await supabase.auth.setSession(session as Session)
      }

      // Initialize/update session security
      try {
        const s = session
        const token =
          s &&
          typeof s === 'object' &&
          'access_token' in s &&
          typeof (s as Record<string, unknown>).access_token === 'string'
            ? ((s as Record<string, unknown>).access_token as string)
            : undefined
        const userId =
          s &&
          typeof s === 'object' &&
          'user' in s &&
          typeof (s as Record<string, unknown>).user === 'object' &&
          (s as Record<string, unknown>).user !== null &&
          'id' in (s as { user?: Record<string, unknown> }).user! &&
          typeof (
            (s as { user?: Record<string, unknown> }).user as Record<
              string,
              unknown
            >
          ).id === 'string'
            ? ((s as { user?: { id?: string } }).user!.id as string)
            : undefined
        if (token && userId) {
          const existing = sessionManager.getSession(token)
          if (!existing) {
            await sessionManager.initializeSession(token, userId, {
              headers: request.headers,
            })
          } else {
            sessionManager.updateActivity(token, { headers: request.headers })
          }
        }
      } catch (e) {
        console.warn('Auth callback POST: session manager init failed', e)
      }
    }

    if (event === 'SIGNED_OUT') {
      await supabase.auth.signOut()
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Auth callback POST error:', error)
    return NextResponse.json(
      { success: false, error: 'callback_failed' },
      { status: 400 }
    )
  }
}

export function OPTIONS() {
  return NextResponse.json({})
}
