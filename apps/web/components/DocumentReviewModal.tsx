'use client'

import React, { useState, useEffect, CSSProperties } from 'react'
import { fetchAccountsMap, getAccountDisplayName } from '@/lib/accounts'
import { normalizeSuggestion } from '@/lib/suggestion'
import type {
  DocumentReviewModalProps,
  DocumentReviewFormData,
  DocumentReviewFormErrors,
  InboxDocument,
  ExtractionResult,
} from '@/types/document-review'

// Zen UI Theme - consistent with existing components
const zenTheme = {
  bg: '#FBFAF5',
  surface: '#FFFFFC',
  primaryText: '#1a1a1a',
  secondaryText: '#6b7280',
  subtleText: '#9ca3af',
  border: '#f3f4f6',
  borderHover: '#e5e7eb',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  primaryAction: '#3b82f6',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)',
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
}

/**
 * Document Review Modal Component
 *
 * Displays AI-extracted data and suggested journal lines for user review and confirmation.
 * Allows editing of extraction results before confirming the document.
 */
export default function DocumentReviewModal({
  isOpen,
  document,
  onClose,
  onConfirm,
  loading = false,
  error = null,
}: DocumentReviewModalProps) {
  const [formData, setFormData] = useState<DocumentReviewFormData>({
    supplierName: '',
    supplierVat: '',
    supplierAddress: '',
    invoiceNumber: '',
    invoiceIssueDate: '',
    invoiceDueDate: '',
    invoiceCurrency: 'EUR',
    invoiceNet: '',
    invoiceVat: '',
    invoiceGross: '',
    paymentIban: '',
    paymentBic: '',
    paymentStructuredRef: '',
    journalDate: '',
    journalReference: '',
    journalDescription: '',
    lines: [],
    suggestionLines: [],
  })

  const [formErrors, setFormErrors] = useState<DocumentReviewFormErrors>({})
  const [submitting, setSubmitting] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [accounts, setAccounts] = useState<
    Map<number, { code: string; name: string }>
  >(new Map())

  // Initialize form data when document changes
  useEffect(() => {
    if (document && isOpen) {
      initializeFormData(document)
    }
  }, [document, isOpen])

  const initializeFormData = (doc: InboxDocument) => {
    const extraction = doc.extraction
    const suggestion = normalizeSuggestion(doc.suggestion)

    setFormData({
      // Supplier information from extraction
      supplierName: extraction?.supplier?.name || doc.supplier_name || '',
      supplierVat: extraction?.supplier?.vat || '',
      supplierAddress: extraction?.supplier?.address || '',

      // Invoice information from extraction
      invoiceNumber: extraction?.invoice?.number || doc.invoice_number || '',
      invoiceIssueDate:
        extraction?.invoice?.issueDate || doc.invoice_date || '',
      invoiceDueDate: extraction?.invoice?.dueDate || '',
      invoiceCurrency: 'EUR',
      invoiceNet:
        extraction?.totals?.net ||
        extraction?.invoice?.net ||
        doc.net_amount ||
        '',
      invoiceVat:
        extraction?.totals?.vat ||
        extraction?.invoice?.vat ||
        doc.vat_amount ||
        '',
      invoiceGross:
        extraction?.totals?.gross ||
        extraction?.invoice?.gross ||
        doc.gross_amount ||
        '',

      paymentIban: extraction?.paymentInstructions?.iban || '',
      paymentBic: extraction?.paymentInstructions?.bic || '',
      paymentStructuredRef:
        extraction?.paymentInstructions?.structuredRef || '',

      // Journal information from suggestion
      journalDate:
        suggestion?.journalDate || extraction?.invoice?.issueDate || '',
      journalReference:
        suggestion?.reference || extraction?.invoice?.number || '',
      journalDescription:
        suggestion?.description ||
        `Invoice from ${extraction?.supplier?.name || 'Supplier'}`,

      // Line items from extraction
      lines:
        extraction?.lines?.map(line => ({
          description: line.description,
          quantity: line.quantity,
          unitPrice: line.unitPrice,
          vatRate: line.vatRate,
          accountHint: line.accountHint,
        })) || [],

      // Suggestion lines for review
      suggestionLines:
        suggestion?.lines?.map(line => ({
          accountId: line.accountId,
          debit: line.debit || '0',
          credit: line.credit || '0',
          vatCodeId: line.vatCodeId,
          memo: line.memo,
        })) || [],
    })

    setFormErrors({})
    setHasChanges(false)
  }

  // Fetch accounts for friendly display if entity_id is available
  useEffect(() => {
    const run = async () => {
      try {
        if (!isOpen || !document?.entity_id) return
        const map = await fetchAccountsMap(document.entity_id)
        setAccounts(map)
      } catch {
        // best-effort; UI will fall back to showing the ID
      }
    }
    void run()
  }, [isOpen, document])

  const handleInputChange = (
    field: keyof DocumentReviewFormData,
    value: string
  ) => {
    // Update only known scalar fields explicitly to avoid dynamic key injection
    setFormData(prev => {
      const next = { ...prev }
      switch (field) {
        case 'supplierName':
          next.supplierName = value
          break
        case 'supplierVat':
          next.supplierVat = value
          break
        case 'supplierAddress':
          next.supplierAddress = value
          break
        case 'invoiceNumber':
          next.invoiceNumber = value
          break
        case 'invoiceIssueDate':
          next.invoiceIssueDate = value
          break
        case 'invoiceDueDate':
          next.invoiceDueDate = value
          break
        case 'invoiceCurrency':
          next.invoiceCurrency =
            value as DocumentReviewFormData['invoiceCurrency']
          break
        case 'invoiceNet':
          next.invoiceNet = value
          break
        case 'invoiceVat':
          next.invoiceVat = value
          break
        case 'invoiceGross':
          next.invoiceGross = value
          break
        case 'paymentIban':
          next.paymentIban = value
          break
        case 'paymentBic':
          next.paymentBic = value
          break
        case 'paymentStructuredRef':
          next.paymentStructuredRef = value
          break
        case 'journalDate':
          next.journalDate = value
          break
        case 'journalReference':
          next.journalReference = value
          break
        case 'journalDescription':
          next.journalDescription = value
          break
        case 'lines':
        case 'suggestionLines':
          return prev
      }
      return next
    })
    setHasChanges(true)

    // Clear field-specific errors
    setFormErrors(prev => {
      const next = { ...prev }
      switch (field) {
        case 'supplierName':
          next.supplierName = undefined
          break
        case 'supplierVat':
          next.supplierVat = undefined
          break
        case 'supplierAddress':
          next.supplierAddress = undefined
          break
        case 'invoiceNumber':
          next.invoiceNumber = undefined
          break
        case 'invoiceIssueDate':
          next.invoiceIssueDate = undefined
          break
        case 'invoiceDueDate':
          next.invoiceDueDate = undefined
          break
        case 'invoiceCurrency':
          next.invoiceCurrency = undefined
          break
        case 'invoiceNet':
          next.invoiceNet = undefined
          break
        case 'invoiceVat':
          next.invoiceVat = undefined
          break
        case 'invoiceGross':
          next.invoiceGross = undefined
          break
        case 'paymentIban':
          next.paymentIban = undefined
          break
        case 'paymentBic':
          next.paymentBic = undefined
          break
        case 'paymentStructuredRef':
          next.paymentStructuredRef = undefined
          break
        case 'journalDate':
          next.journalDate = undefined
          break
        case 'journalReference':
          next.journalReference = undefined
          break
        case 'journalDescription':
          next.journalDescription = undefined
          break
        default:
          return prev
      }
      return next
    })
  }

  const validateForm = (): boolean => {
    const errors: DocumentReviewFormErrors = {}

    // Required fields validation
    if (!formData.supplierName.trim()) {
      errors.supplierName = 'Supplier name is required'
    }

    if (!formData.invoiceNumber.trim()) {
      errors.invoiceNumber = 'Invoice number is required'
    }

    if (!formData.invoiceIssueDate.trim()) {
      errors.invoiceIssueDate = 'Invoice date is required'
    }

    if (!formData.invoiceGross.trim()) {
      errors.invoiceGross = 'Invoice amount is required'
    }

    // Date format validation (avoid regex for security plugin compatibility)
    const isValidDate = (s: string) => {
      if (s.length !== 10) return false
      const [y, m, d] = s.split('-')
      if (!y || !m || !d || y.length !== 4 || m.length !== 2 || d.length !== 2)
        return false
      const year = Number(y)
      const month = Number(m)
      const day = Number(d)
      if (
        !Number.isInteger(year) ||
        !Number.isInteger(month) ||
        !Number.isInteger(day)
      )
        return false
      if (month < 1 || month > 12) return false
      if (day < 1 || day > 31) return false
      return true
    }
    if (formData.invoiceIssueDate && !isValidDate(formData.invoiceIssueDate)) {
      errors.invoiceIssueDate = 'Date must be in YYYY-MM-DD format'
    }

    if (formData.invoiceDueDate && !isValidDate(formData.invoiceDueDate)) {
      errors.invoiceDueDate = 'Date must be in YYYY-MM-DD format'
    }

    // Amount validation without regex
    const isValidAmount = (s: string) => {
      if (s.trim() === '') return false
      let dotCount = 0
      for (const ch of s) {
        if (ch === '.') {
          dotCount++
          if (dotCount > 1) return false
        } else if (ch < '0' || ch > '9') {
          return false
        }
      }
      const parts = s.split('.')
      return parts.length < 2 || parts[1].length <= 2
    }
    if (formData.invoiceNet && !isValidAmount(formData.invoiceNet)) {
      errors.invoiceNet = 'Invalid amount format'
    }

    if (formData.invoiceVat && !isValidAmount(formData.invoiceVat)) {
      errors.invoiceVat = 'Invalid amount format'
    }

    if (formData.invoiceGross && !isValidAmount(formData.invoiceGross)) {
      errors.invoiceGross = 'Invalid amount format'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleConfirm = async () => {
    if (!document || !validateForm()) {
      return
    }

    setSubmitting(true)
    try {
      // Build corrections object if there are changes
      let corrections: Partial<ExtractionResult> | undefined

      if (hasChanges) {
        corrections = {
          supplier: {
            name: formData.supplierName,
            vat: formData.supplierVat || undefined,
            address: formData.supplierAddress || undefined,
          },
          invoice: {
            number: formData.invoiceNumber,
            issueDate: formData.invoiceIssueDate,
            dueDate: formData.invoiceDueDate || undefined,
            currency: formData.invoiceCurrency,
          },
          totals: {
            net: formData.invoiceNet || undefined,
            vat: formData.invoiceVat || undefined,
            gross: formData.invoiceGross || undefined,
            currency: formData.invoiceCurrency,
          },
          paymentInstructions: {
            iban: formData.paymentIban || undefined,
            bic: formData.paymentBic || undefined,
            structuredRef: formData.paymentStructuredRef || undefined,
          },
          lines:
            formData.lines?.map(line => ({
              description: line.description,
              quantity: line.quantity,
              unitPrice: line.unitPrice,
              vatRate: line.vatRate,
              accountHint: line.accountHint,
            })) || [],
        }
      }

      await onConfirm(document.id, corrections)
      onClose()
    } catch (err) {
      console.error('Error confirming document:', err)
      setFormErrors({
        general: 'Failed to confirm document. Please try again.',
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleClose = () => {
    if (hasChanges) {
      const confirmClose = window.confirm(
        'You have unsaved changes. Are you sure you want to close?'
      )
      if (!confirmClose) return
    }
    onClose()
  }

  // Style functions
  const getOverlayStyle = (): CSSProperties => ({
    position: 'fixed',
    inset: 0,
    zIndex: 50,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    padding: '16px',
  })

  const getModalStyle = (): CSSProperties => ({
    background: zenTheme.surface,
    borderRadius: '12px',
    padding: '32px',
    width: '100%',
    maxWidth: '800px',
    maxHeight: '90vh',
    overflowY: 'auto',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
  })

  const getHeaderStyle = (): CSSProperties => ({
    marginBottom: '24px',
    paddingBottom: '16px',
    borderBottom: `1px solid ${zenTheme.border}`,
  })

  const getTitleStyle = (): CSSProperties => ({
    fontSize: '24px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    margin: '0 0 8px 0',
  })

  const getSubtitleStyle = (): CSSProperties => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: 0,
  })

  const getSectionStyle = (): CSSProperties => ({
    marginBottom: '24px',
  })

  const getSectionTitleStyle = (): CSSProperties => ({
    fontSize: '18px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    margin: '0 0 16px 0',
  })

  const getFormRowStyle = (): CSSProperties => ({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '16px',
    marginBottom: '16px',
  })

  const getFormGroupStyle = (): CSSProperties => ({
    marginBottom: '16px',
  })

  const getLabelStyle = (): CSSProperties => ({
    display: 'block',
    fontSize: '14px',
    fontWeight: 500,
    color: zenTheme.primaryText,
    marginBottom: '6px',
  })

  const getInputStyle = (hasError: boolean = false): CSSProperties => ({
    width: '100%',
    padding: '10px 12px',
    border: `1px solid ${hasError ? zenTheme.error : zenTheme.border}`,
    borderRadius: '6px',
    fontSize: '14px',
    color: zenTheme.primaryText,
    backgroundColor: zenTheme.surface,
    transition: 'border-color 0.15s ease',
  })

  const getErrorStyle = (): CSSProperties => ({
    fontSize: '12px',
    color: zenTheme.error,
    marginTop: '4px',
  })

  const getButtonRowStyle = (): CSSProperties => ({
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
    paddingTop: '24px',
    borderTop: `1px solid ${zenTheme.border}`,
    marginTop: '24px',
  })

  const getButtonStyle = (
    variant: 'primary' | 'secondary' = 'secondary'
  ): CSSProperties => ({
    padding: '10px 20px',
    borderRadius: '6px',
    fontSize: '14px',
    fontWeight: 500,
    border: variant === 'primary' ? 'none' : `1px solid ${zenTheme.border}`,
    backgroundColor:
      variant === 'primary' ? zenTheme.primaryAction : zenTheme.surface,
    color: variant === 'primary' ? '#ffffff' : zenTheme.primaryText,
    cursor: 'pointer',
    transition: 'all 0.15s ease',
  })

  if (!isOpen) return null

  return (
    <div style={getOverlayStyle()}>
      <div style={getModalStyle()}>
        {/* Header */}
        <div style={getHeaderStyle()}>
          <h2 style={getTitleStyle()}>Review Document</h2>
          <p style={getSubtitleStyle()}>
            Review and edit the extracted information before confirming
          </p>
        </div>

        {/* Payment Instructions */}
        <div style={getSectionStyle()}>
          <h3 style={getSectionTitleStyle()}>Payment Instructions</h3>

          <div style={getFormRowStyle()}>
            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>IBAN</label>
              <input
                type="text"
                value={formData.paymentIban}
                onChange={e => handleInputChange('paymentIban', e.target.value)}
                style={getInputStyle(!!formErrors.paymentIban)}
                placeholder="BE00 0000 0000 0000"
              />
            </div>

            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>BIC</label>
              <input
                type="text"
                value={formData.paymentBic}
                onChange={e => handleInputChange('paymentBic', e.target.value)}
                style={getInputStyle(!!formErrors.paymentBic)}
                placeholder="BBRUBEBB"
              />
            </div>
          </div>

          <div style={getFormGroupStyle()}>
            <label style={getLabelStyle()}>Structured Reference</label>
            <input
              type="text"
              value={formData.paymentStructuredRef}
              onChange={e =>
                handleInputChange('paymentStructuredRef', e.target.value)
              }
              style={getInputStyle(!!formErrors.paymentStructuredRef)}
              placeholder="+++123/4567/89012+++"
            />
          </div>
        </div>

        {/* Error Display */}
        {(error || formErrors.general) && (
          <div
            style={{
              padding: '12px',
              backgroundColor: '#fef2f2',
              border: `1px solid ${zenTheme.error}`,
              borderRadius: '6px',
              marginBottom: '24px',
            }}
          >
            <p style={{ color: zenTheme.error, margin: 0, fontSize: '14px' }}>
              {error || formErrors.general}
            </p>
          </div>
        )}

        {/* Supplier Information */}
        <div style={getSectionStyle()}>
          <h3 style={getSectionTitleStyle()}>Supplier Information</h3>

          <div style={getFormRowStyle()}>
            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>Supplier Name *</label>
              <input
                type="text"
                value={formData.supplierName}
                onChange={e =>
                  handleInputChange('supplierName', e.target.value)
                }
                style={getInputStyle(!!formErrors.supplierName)}
                placeholder="Enter supplier name"
              />
              {formErrors.supplierName && (
                <div style={getErrorStyle()}>{formErrors.supplierName}</div>
              )}
            </div>

            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>VAT Number</label>
              <input
                type="text"
                value={formData.supplierVat}
                onChange={e => handleInputChange('supplierVat', e.target.value)}
                style={getInputStyle(!!formErrors.supplierVat)}
                placeholder="BE0123456789"
              />
              {formErrors.supplierVat && (
                <div style={getErrorStyle()}>{formErrors.supplierVat}</div>
              )}
            </div>
          </div>

          <div style={getFormGroupStyle()}>
            <label style={getLabelStyle()}>Address</label>
            <input
              type="text"
              value={formData.supplierAddress}
              onChange={e =>
                handleInputChange('supplierAddress', e.target.value)
              }
              style={getInputStyle()}
              placeholder="Enter supplier address"
            />
          </div>
        </div>

        {/* Invoice Information */}
        <div style={getSectionStyle()}>
          <h3 style={getSectionTitleStyle()}>Invoice Information</h3>

          <div style={getFormRowStyle()}>
            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>Invoice Number *</label>
              <input
                type="text"
                value={formData.invoiceNumber}
                onChange={e =>
                  handleInputChange('invoiceNumber', e.target.value)
                }
                style={getInputStyle(!!formErrors.invoiceNumber)}
                placeholder="Enter invoice number"
              />
              {formErrors.invoiceNumber && (
                <div style={getErrorStyle()}>{formErrors.invoiceNumber}</div>
              )}
            </div>

            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>Invoice Date *</label>
              <input
                type="date"
                value={formData.invoiceIssueDate}
                onChange={e =>
                  handleInputChange('invoiceIssueDate', e.target.value)
                }
                style={getInputStyle(!!formErrors.invoiceIssueDate)}
              />
              {formErrors.invoiceIssueDate && (
                <div style={getErrorStyle()}>{formErrors.invoiceIssueDate}</div>
              )}
            </div>
          </div>

          <div style={getFormRowStyle()}>
            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>Due Date</label>
              <input
                type="date"
                value={formData.invoiceDueDate}
                onChange={e =>
                  handleInputChange('invoiceDueDate', e.target.value)
                }
                style={getInputStyle(!!formErrors.invoiceDueDate)}
              />
              {formErrors.invoiceDueDate && (
                <div style={getErrorStyle()}>{formErrors.invoiceDueDate}</div>
              )}
            </div>

            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>Currency</label>
              <select
                value={formData.invoiceCurrency}
                onChange={e =>
                  handleInputChange('invoiceCurrency', e.target.value)
                }
                style={getInputStyle()}
              >
                <option value="EUR">EUR</option>
              </select>
            </div>
          </div>

          <div style={getFormRowStyle()}>
            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>Net Amount</label>
              <input
                type="text"
                value={formData.invoiceNet}
                onChange={e => handleInputChange('invoiceNet', e.target.value)}
                style={getInputStyle(!!formErrors.invoiceNet)}
                placeholder="0.00"
              />
              {formErrors.invoiceNet && (
                <div style={getErrorStyle()}>{formErrors.invoiceNet}</div>
              )}
            </div>

            <div style={getFormGroupStyle()}>
              <label style={getLabelStyle()}>VAT Amount</label>
              <input
                type="text"
                value={formData.invoiceVat}
                onChange={e => handleInputChange('invoiceVat', e.target.value)}
                style={getInputStyle(!!formErrors.invoiceVat)}
                placeholder="0.00"
              />
              {formErrors.invoiceVat && (
                <div style={getErrorStyle()}>{formErrors.invoiceVat}</div>
              )}
            </div>
          </div>

          <div style={getFormGroupStyle()}>
            <label style={getLabelStyle()}>Total Amount *</label>
            <input
              type="text"
              value={formData.invoiceGross}
              onChange={e => handleInputChange('invoiceGross', e.target.value)}
              style={getInputStyle(!!formErrors.invoiceGross)}
              placeholder="0.00"
            />
            {formErrors.invoiceGross && (
              <div style={getErrorStyle()}>{formErrors.invoiceGross}</div>
            )}
          </div>
        </div>

        {/* Suggested Journal Lines */}
        {formData.suggestionLines.length > 0 && (
          <div style={getSectionStyle()}>
            <h3 style={getSectionTitleStyle()}>Suggested Journal Lines</h3>
            <div
              style={{
                border: `1px solid ${zenTheme.border}`,
                borderRadius: '6px',
                overflow: 'hidden',
              }}
            >
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '2fr 1fr 1fr 1fr',
                  gap: '12px',
                  padding: '12px',
                  backgroundColor: zenTheme.bg,
                  borderBottom: `1px solid ${zenTheme.border}`,
                  fontSize: '14px',
                  fontWeight: 600,
                  color: zenTheme.primaryText,
                }}
              >
                <div>Account</div>
                <div>Debit</div>
                <div>Credit</div>
                <div>Memo</div>
              </div>
              {formData.suggestionLines.map((line, index) => (
                <div
                  key={index}
                  style={{
                    display: 'grid',
                    gridTemplateColumns: '2fr 1fr 1fr 1fr',
                    gap: '12px',
                    padding: '12px',
                    borderBottom:
                      index < formData.suggestionLines.length - 1
                        ? `1px solid ${zenTheme.border}`
                        : 'none',
                    fontSize: '14px',
                    color: zenTheme.primaryText,
                  }}
                >
                  <div>
                    {getAccountDisplayName(
                      accounts,
                      line.accountId as unknown as number
                    )}
                  </div>
                  <div>€{parseFloat(line.debit || '0').toFixed(2)}</div>
                  <div>€{parseFloat(line.credit || '0').toFixed(2)}</div>
                  <div style={{ color: zenTheme.secondaryText }}>
                    {line.memo || '-'}
                  </div>
                </div>
              ))}

              {/* Balance Check */}
              <div
                style={{
                  padding: '12px',
                  backgroundColor: zenTheme.bg,
                  borderTop: `1px solid ${zenTheme.border}`,
                  fontSize: '14px',
                  fontWeight: 600,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    color: zenTheme.primaryText,
                  }}
                >
                  <span>Balance Check:</span>
                  <span
                    style={{
                      color: (() => {
                        const totalDebits = formData.suggestionLines.reduce(
                          (sum, line) => sum + parseFloat(line.debit || '0'),
                          0
                        )
                        const totalCredits = formData.suggestionLines.reduce(
                          (sum, line) => sum + parseFloat(line.credit || '0'),
                          0
                        )
                        const isBalanced =
                          Math.abs(totalDebits - totalCredits) < 0.01
                        return isBalanced ? zenTheme.success : zenTheme.error
                      })(),
                    }}
                  >
                    {(() => {
                      const totalDebits = formData.suggestionLines.reduce(
                        (sum, line) => sum + parseFloat(line.debit || '0'),
                        0
                      )
                      const totalCredits = formData.suggestionLines.reduce(
                        (sum, line) => sum + parseFloat(line.credit || '0'),
                        0
                      )
                      const isBalanced =
                        Math.abs(totalDebits - totalCredits) < 0.01
                      return isBalanced
                        ? 'Balanced ✓'
                        : `Unbalanced (${(totalDebits - totalCredits).toFixed(2)})`
                    })()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div style={getButtonRowStyle()}>
          <button
            type="button"
            onClick={handleClose}
            style={getButtonStyle('secondary')}
            disabled={submitting}
          >
            Cancel
          </button>

          <button
            type="button"
            onClick={() => {
              void handleConfirm()
            }}
            style={getButtonStyle('primary')}
            disabled={submitting || loading}
          >
            {submitting ? 'Confirming...' : 'Confirm Document'}
          </button>
        </div>
      </div>
    </div>
  )
}
