import { renderHook, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useInboxDocuments } from '@/hooks/useInboxDocuments'

// Mock the dependencies
vi.mock('@/lib/session-security', () => ({
  createSecureBrowserClient: vi.fn(() => ({
    auth: {
      getSession: vi.fn(() => Promise.resolve({
        data: {
          session: {
            access_token: 'mock-token'
          }
        }
      }))
    },
    channel: vi.fn(() => ({
      on: vi.fn(() => ({
        subscribe: vi.fn()
      })),
      unsubscribe: vi.fn()
    }))
  }))
}))

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'mock-user-id' }
  }))
}))

vi.mock('@/hooks/useOrgEntitySelection', () => ({
  useOrgEntitySelection: vi.fn(() => ({
    currentEntity: { entity_id: 123 },
    isValid: true
  }))
}))

// Mock fetch
global.fetch = vi.fn()

describe('useInboxDocuments', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Setup default fetch mock
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          documents: [
            {
              id: 1,
              entity_id: 123,
              path: 'test/document.pdf',
              mime_type: 'application/pdf',
              source: 'upload',
              status: 'uploaded',
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z'
            }
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 1,
            pages: 1
          }
        }
      })
    })
  })

  it('should fetch documents successfully', async () => {
    const { result } = renderHook(() => useInboxDocuments())

    // Initially loading
    expect(result.current.loading).toBe(true)
    expect(result.current.documents).toEqual([])

    // Wait for the fetch to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Should have fetched documents
    expect(result.current.documents).toHaveLength(1)
    expect(result.current.documents[0].id).toBe(1)
    expect(result.current.pagination?.total).toBe(1)
    expect(result.current.error).toBeNull()
  })

  it('should handle fetch errors', async () => {
    // Mock fetch to return an error
    ;(global.fetch as any).mockResolvedValue({
      ok: false,
      status: 500,
      text: () => Promise.resolve('Internal Server Error')
    })

    const { result } = renderHook(() => useInboxDocuments())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBeTruthy()
    expect(result.current.documents).toEqual([])
  })

  it('should call the correct API endpoint with parameters', async () => {
    renderHook(() => useInboxDocuments({ status: 'uploaded', page: 2, limit: 5 }))

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/entities/123/documents?page=2&limit=5&status=uploaded'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          })
        })
      )
    })
  })
})
