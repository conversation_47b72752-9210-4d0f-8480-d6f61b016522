import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import {
  rpcVatPreview,
  checkVATEnabled,
  createVATDisabledError,
} from '@belbooks/dal'
import type { Database, VATError } from '@belbooks/types'

function isVatError(err: unknown): err is VATError {
  return err instanceof Error && 'code' in err
}

export async function GET(
  request: NextRequest,
  { params }: { params: { entityId: string } }
): Promise<Response> {
  try {
    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url)
    const start = searchParams.get('start')
    const end = searchParams.get('end')

    if (!start || !end) {
      return createJsonResponse(
        { error: 'Start and end date parameters are required' },
        400
      )
    }

    const entityId = parseInt(params.entityId, 10)

    // Check VAT feature flag using typed DAL function
    const isVATEnabled = await checkVATEnabled(supabase, entityId)
    if (!isVATEnabled) {
      const vatError = createVATDisabledError()
      return createJsonResponse({ error: vatError.message }, 403)
    }

    // Call the typed VAT preview function
    const vatPreviewData = await rpcVatPreview(supabase, {
      p_entity: entityId,
      p_start: start,
      p_end: end,
    })

    const response = {
      success: true,
      data: vatPreviewData,
    }

    return createJsonResponse(response, 200)
  } catch (error: unknown) {
    console.error('VAT preview API error:', error)

    // Handle VAT-specific errors
    if (isVatError(error)) {
      const vatError = error
      let statusCode = 500

      if (vatError.code === 'INVALID_DATE_FORMAT') {
        statusCode = 400
      } else if (vatError.code === 'VAT_DISABLED') {
        statusCode = 403
      }

      return createJsonResponse({ error: vatError.message }, statusCode)
    }

    const message =
      error instanceof Error ? error.message : 'Internal server error'
    return createJsonResponse({ error: message }, 500)
  }
}
