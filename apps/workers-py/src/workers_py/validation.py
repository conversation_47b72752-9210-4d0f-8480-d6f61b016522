"""Validation utilities for extraction results."""

import logging
import re
from decimal import Decimal, InvalidOperation

from .models import ExtractionResult, VatRate
from .config import settings

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Validation error with details."""

    def __init__(self, message: str, field: str = None, code: str = None):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(message)


class ValidationResult:
    """Container for validation results."""

    def __init__(self):
        self.is_valid = True
        self.errors: list[ValidationError] = []
        self.warnings: list[str] = []
        self.confidence_adjustments: list[tuple[str, float]] = []
        self.final_confidence_multiplier = 1.0

    def add_error(self, message: str, field: str = None, code: str = None):
        """Add validation error."""
        self.is_valid = False
        self.errors.append(ValidationError(message, field, code))

    def add_warning(self, message: str):
        """Add validation warning."""
        self.warnings.append(message)

    def adjust_confidence(self, reason: str, multiplier: float):
        """Adjust confidence with reason."""
        self.confidence_adjustments.append((reason, multiplier))
        self.final_confidence_multiplier *= multiplier

    def to_dict(self) -> dict:
        """Convert to dictionary for logging/storage."""
        return {
            "is_valid": self.is_valid,
            "errors": [
                {
                    "message": e.message,
                    "field": e.field,
                    "code": e.code
                } for e in self.errors
            ],
            "warnings": self.warnings,
            "confidence_adjustments": self.confidence_adjustments,
            "final_confidence_multiplier": self.final_confidence_multiplier
        }


class ExtractionValidator:
    """Validator for extraction results with Belgian invoice rules."""

    def __init__(self):
        """Initialize validator."""
        self.belgian_vat_pattern = re.compile(r'^BE0\d{9}$')
        self.iban_pattern = re.compile(r'^[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}$')
        self.date_pattern = re.compile(r'^\d{4}-\d{2}-\d{2}$')

    def validate_extraction(
        self,
        extraction: ExtractionResult,
        original_confidence: float
    ) -> tuple[ValidationResult, float]:
        """
        Validate extraction result and adjust confidence.

        Args:
            extraction: ExtractionResult to validate
            original_confidence: Original confidence from extraction

        Returns:
            Tuple of (ValidationResult, adjusted_confidence)
        """
        result = ValidationResult()

        # Core validations
        self._validate_supplier(extraction.supplier, result)
        self._validate_invoice(extraction.invoice, result)
        self._validate_lines(extraction.lines, result)

        # Cross-field validations
        self._validate_arithmetic_consistency(extraction, result)
        self._validate_business_logic(extraction, result)

        # Calculate final confidence
        final_confidence = original_confidence * result.final_confidence_multiplier
        final_confidence = max(0.0, min(1.0, final_confidence))  # Clamp to [0,1]

        logger.info(f"Validation completed: valid={result.is_valid}, "
                   f"confidence {original_confidence:.3f} → {final_confidence:.3f}")

        return result, final_confidence

    def _validate_supplier(self, supplier, result: ValidationResult):
        """Validate supplier information."""
        if not supplier.name or not supplier.name.strip():
            result.add_error("Supplier name is required", "supplier.name", "MISSING_SUPPLIER_NAME")
        elif len(supplier.name.strip()) < 2:
            result.add_error("Supplier name too short", "supplier.name", "INVALID_SUPPLIER_NAME")

        # VAT number validation
        if supplier.vat:
            if not self.belgian_vat_pattern.match(supplier.vat.replace(' ', '')):
                result.add_warning(f"Supplier VAT '{supplier.vat}' doesn't match Belgian format")
                result.adjust_confidence("Invalid VAT format", 0.9)
        else:
            result.add_warning("Supplier VAT number not provided")
            result.adjust_confidence("Missing VAT", 0.95)

        # IBAN validation
        if supplier.iban:
            clean_iban = supplier.iban.replace(' ', '')
            if not self.iban_pattern.match(clean_iban):
                result.add_warning(f"Supplier IBAN '{supplier.iban}' format appears invalid")
                result.adjust_confidence("Invalid IBAN format", 0.95)

    def _validate_invoice(self, invoice, result: ValidationResult):
        """Validate invoice header information."""

        # Invoice number
        if not invoice.number or not invoice.number.strip():
            result.add_error("Invoice number is required", "invoice.number", "MISSING_INVOICE_NUMBER")

        # Issue date
        if not invoice.issue_date:
            result.add_error("Invoice issue date is required", "invoice.issue_date", "MISSING_ISSUE_DATE")
        elif not self.date_pattern.match(invoice.issue_date):
            result.add_error(
                f"Invoice issue date '{invoice.issue_date}' must be in YYYY-MM-DD format",
                "invoice.issue_date",
                "INVALID_DATE_FORMAT"
            )

        # Due date (optional but must be valid if present)
        if invoice.due_date and not self.date_pattern.match(invoice.due_date):
            result.add_error(
                f"Invoice due date '{invoice.due_date}' must be in YYYY-MM-DD format",
                "invoice.due_date",
                "INVALID_DATE_FORMAT"
            )

        # Currency
        if invoice.currency != "EUR":
            result.add_warning(f"Non-EUR currency detected: {invoice.currency}")
            result.adjust_confidence("Non-EUR currency", 0.9)

        # Amounts
        self._validate_amounts(invoice, result)

    def _validate_amounts(self, invoice, result: ValidationResult):
        """Validate invoice amounts."""
        try:
            net = Decimal(invoice.net) if invoice.net else Decimal('0')
            vat = Decimal(invoice.vat) if invoice.vat else Decimal('0')
            gross = Decimal(invoice.gross) if invoice.gross else Decimal('0')

            # All amounts should be positive
            if net < 0:
                result.add_error("Net amount cannot be negative", "invoice.net", "NEGATIVE_AMOUNT")
            if vat < 0:
                result.add_error("VAT amount cannot be negative", "invoice.vat", "NEGATIVE_AMOUNT")
            if gross < 0:
                result.add_error("Gross amount cannot be negative", "invoice.gross", "NEGATIVE_AMOUNT")

            # Net should be less than or equal to gross
            if net > gross:
                result.add_error("Net amount cannot exceed gross amount", "invoice.net", "INVALID_NET_GROSS")

        except (InvalidOperation, ValueError, TypeError) as e:
            result.add_error(f"Invalid amount format: {e}", "invoice.amounts", "INVALID_AMOUNT_FORMAT")

    def _validate_lines(self, lines, result: ValidationResult):
        """Validate invoice line items."""
        if not lines:
            result.add_error("At least one line item is required", "lines", "MISSING_LINE_ITEMS")
            return

        for i, line in enumerate(lines):
            line_prefix = f"lines[{i}]"

            # Description
            if not line.description or not line.description.strip():
                result.add_error(
                    f"Line {i+1}: Description is required",
                    f"{line_prefix}.description",
                    "MISSING_LINE_DESCRIPTION"
                )

            # Quantity and unit price
            try:
                qty = Decimal(line.quantity) if line.quantity else Decimal('1')
                price = Decimal(line.unit_price) if line.unit_price else Decimal('0')

                if qty <= 0:
                    result.add_error(
                        f"Line {i+1}: Quantity must be positive",
                        f"{line_prefix}.quantity",
                        "INVALID_QUANTITY"
                    )

                if price < 0:
                    result.add_error(
                        f"Line {i+1}: Unit price cannot be negative",
                        f"{line_prefix}.unit_price",
                        "NEGATIVE_PRICE"
                    )

            except (InvalidOperation, ValueError, TypeError):
                result.add_error(
                    f"Line {i+1}: Invalid quantity or price format",
                    f"{line_prefix}.amounts",
                    "INVALID_LINE_AMOUNTS"
                )

            # VAT rate
            if line.vat_rate not in [VatRate.STANDARD, VatRate.REDUCED, VatRate.LOW, VatRate.ZERO]:
                result.add_error(
                    f"Line {i+1}: Invalid VAT rate {line.vat_rate}% (must be 0, 6, 12, or 21)",
                    f"{line_prefix}.vat_rate",
                    "INVALID_VAT_RATE"
                )

    def _validate_arithmetic_consistency(self, extraction: ExtractionResult, result: ValidationResult):
        """Validate arithmetic consistency across invoice."""
        try:
            invoice = extraction.invoice
            net = Decimal(invoice.net) if invoice.net else Decimal('0')
            vat = Decimal(invoice.vat) if invoice.vat else Decimal('0')
            gross = Decimal(invoice.gross) if invoice.gross else Decimal('0')

            # Check net + vat = gross (within 1 cent tolerance)
            calculated_gross = net + vat
            difference = abs(calculated_gross - gross)

            if difference > Decimal('0.01'):
                result.add_error(
                    f"Arithmetic inconsistency: Net ({net}) + VAT ({vat}) = {calculated_gross}, "
                    f"but Gross is {gross} (difference: {difference})",
                    "invoice.amounts",
                    "ARITHMETIC_MISMATCH"
                )
            elif difference > Decimal('0.005'):
                result.add_warning(
                    f"Minor arithmetic inconsistency: {difference} cent difference"
                )
                result.adjust_confidence("Minor arithmetic inconsistency", 0.95)

            # Validate line totals if possible
            self._validate_line_totals(extraction, result)

        except (InvalidOperation, ValueError, TypeError) as e:
            result.add_warning(f"Could not validate arithmetic consistency: {e}")
            result.adjust_confidence("Arithmetic validation failed", 0.9)

    def _validate_line_totals(self, extraction: ExtractionResult, result: ValidationResult):
        """Validate that line totals match invoice totals."""
        try:
            line_net_total = Decimal('0')
            line_vat_total = Decimal('0')

            for line in extraction.lines:
                qty = Decimal(line.quantity) if line.quantity else Decimal('1')
                price = Decimal(line.unit_price) if line.unit_price else Decimal('0')
                vat_rate = Decimal(line.vat_rate.value) / Decimal('100')

                line_net = qty * price
                line_vat = line_net * vat_rate

                line_net_total += line_net
                line_vat_total += line_vat

            invoice_net = Decimal(extraction.invoice.net) if extraction.invoice.net else Decimal('0')
            invoice_vat = Decimal(extraction.invoice.vat) if extraction.invoice.vat else Decimal('0')

            # Check totals (with some tolerance for rounding)
            net_diff = abs(line_net_total - invoice_net)
            vat_diff = abs(line_vat_total - invoice_vat)

            if net_diff > Decimal('0.10'):
                if settings.VALIDATOR_STRICT_TOTALS:
                    result.add_error(
                        f"Line net totals ({line_net_total}) don't match invoice net ({invoice_net})",
                        "lines",
                        "LINE_NET_MISMATCH",
                    )
                else:
                    result.add_warning(
                        f"Line net totals ({line_net_total}) don't match invoice net ({invoice_net})"
                    )
                    result.adjust_confidence("Line total mismatch", 0.9)

            if vat_diff > Decimal('0.10'):
                if settings.VALIDATOR_STRICT_TOTALS:
                    result.add_error(
                        f"Line VAT totals ({line_vat_total}) don't match invoice VAT ({invoice_vat})",
                        "lines",
                        "LINE_VAT_MISMATCH",
                    )
                else:
                    result.add_warning(
                        f"Line VAT totals ({line_vat_total}) don't match invoice VAT ({invoice_vat})"
                    )
                    result.adjust_confidence("VAT total mismatch", 0.9)

        except (InvalidOperation, ValueError, TypeError) as e:
            logger.debug(f"Could not validate line totals: {e}")
            # Don't penalize confidence for this, as line-level details might be approximated

    def _validate_business_logic(self, extraction: ExtractionResult, result: ValidationResult):
        """Validate business logic rules."""

        # Date logic
        if (extraction.invoice.issue_date and extraction.invoice.due_date):
            try:
                from datetime import datetime
                issue = datetime.strptime(extraction.invoice.issue_date, '%Y-%m-%d')
                due = datetime.strptime(extraction.invoice.due_date, '%Y-%m-%d')

                if due < issue:
                    result.add_error(
                        "Due date cannot be before issue date",
                        "invoice.due_date",
                        "INVALID_DATE_ORDER"
                    )

                # Warn for very short or very long payment terms
                days_diff = (due - issue).days
                if days_diff < 1:
                    result.add_warning("Same-day payment terms are unusual")
                elif days_diff > 180:
                    result.add_warning(f"Very long payment terms: {days_diff} days")
                    result.adjust_confidence("Unusual payment terms", 0.95)

            except ValueError:
                pass  # Date format errors already caught elsewhere

        # Amount reasonableness
        try:
            gross = Decimal(extraction.invoice.gross) if extraction.invoice.gross else Decimal('0')

            if gross == 0:
                result.add_warning("Zero-amount invoice")
                result.adjust_confidence("Zero amount", 0.8)
            elif gross > Decimal('100000'):  # €100k
                result.add_warning(f"Very high invoice amount: {gross}")
                result.adjust_confidence("Very high amount", 0.95)

        except (InvalidOperation, ValueError, TypeError):
            pass


def validate_extraction_result(
    extraction: ExtractionResult,
    original_confidence: float
) -> tuple[ValidationResult, float]:
    """
    Validate extraction result and return adjusted confidence.

    Convenience function for validation.
    """
    validator = ExtractionValidator()
    return validator.validate_extraction(extraction, original_confidence)
