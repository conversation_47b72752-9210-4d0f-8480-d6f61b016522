'use client'

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useMemo,
  useCallback,
} from 'react'
import type { User } from '@supabase/supabase-js'
import { createSecureBrowserClient } from '@/lib/session-security'
import {
  useSessionSecurity,
  useSessionTimeoutDialog,
  SessionSecurityHook,
} from '@/hooks/useSessionSecurity'
import { SessionTimeoutDialog } from '@/components/SessionTimeoutDialog'

interface AuthContextType {
  user: User | null
  session: MinimalSession | null
  loading: boolean
  signOut: () => Promise<void>
  sessionSecurity: SessionSecurityHook
}

const AuthContext = createContext<AuthContextType | null>(null)

interface AuthProviderProps {
  children: ReactNode
}

// Minimal session shape needed client-side
type MinimalSession = { access_token: string; user: User } & Record<string, unknown>

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<MinimalSession | null>(null)
  const [loading, setLoading] = useState(true)
  const [supabase] = useState(() => {
    console.log('DEBUG AuthProvider: Creating Supabase client...')
    try {
      const client = createSecureBrowserClient()
      console.log('DEBUG AuthProvider: Supabase client created successfully')
      return client
    } catch (error) {
      console.error('DEBUG AuthProvider: Error creating Supabase client:', error)
      throw error
    }
  })

  // Safe accessor for user from any session-like object
  const getUserFromSession = (s: unknown): User | null => {
    try {
      return (s as any)?.user ?? null
    } catch {
      return null
    }
  }

  console.log('DEBUG AuthProvider: Render with state:', {
    hasUser: !!user,
    hasSession: !!session,
    loading,
    userId: user?.id
  })

  // Define signOut function early so it can be used in useSessionSecurity
  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
      // Force sign out by clearing local state
      setUser(null)
      setSession(null)
    }
  }, [supabase])

  // Session timeout dialog
  const timeoutDialog = useSessionTimeoutDialog()

  // Create stable empty functions to prevent infinite re-renders
  const emptyTimeoutWarning = useCallback(() => {}, [])
  const emptyTimeout = useCallback(() => {}, [])
  const emptyActivityDetected = useCallback(() => {}, [])

  // Memoize the session security options to prevent infinite re-renders
  const sessionSecurityOptions = useMemo(
    () => ({
      onTimeoutWarning: emptyTimeoutWarning,
      onTimeout: emptyTimeout,
      onActivityDetected: emptyActivityDetected,
      enableActivityTracking: true,
      enableOnlineStatus: true,
      enableAutoRefresh: true,
    }),
    [emptyTimeoutWarning, emptyTimeout, emptyActivityDetected]
  )

  // Memoize the auth data to prevent infinite re-renders
  const authData = useMemo(
    () => ({
      user,
      session,
      signOut,
    }),
    [user, session, signOut]
  )

  // Session security management - temporarily use minimal implementation
  const sessionSecurity = {
    isActive: !!session,
    timeoutInfo: null,
    showWarning: false,
    lastActivity: new Date(),
    isOnline: true,
    extendSession: async () => {},
    forceLogout: () => {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      signOut()
    },
    updateActivity: () => {},
  }

  useEffect(() => {
    console.log('DEBUG AuthContext: useEffect triggered')
    let isInitialLoad = true
    let lastSyncedSessionId: string | null = null

    // Get initial session and ensure server cookies are synced for middleware
    ;(async () => {
      console.log('DEBUG AuthContext: Starting session initialization...')

      // Try to get the current session
      const { data: initialData, error: sessionError } = await supabase.auth.getSession()
      const initialSession = (initialData?.session ?? null) as unknown as MinimalSession | null

      console.log('DEBUG AuthContext: getSession result:', {
        hasSession: !!initialSession,
        hasUser: !!initialSession?.user,
        userId: initialSession?.user?.id,
        sessionError
      })

      // If no session, try to refresh
      if (!initialSession && !sessionError) {
        console.log('DEBUG AuthContext: No session found, trying to refresh...')
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession()
        const refreshedSession = (refreshData?.session ?? null) as unknown as MinimalSession | null

        console.log('DEBUG AuthContext: refreshSession result:', {
          hasSession: !!refreshedSession,
          hasUser: !!refreshedSession?.user,
          userId: refreshedSession?.user?.id,
          refreshError
        })

        if (refreshedSession) {
          setSession(refreshedSession)
          setUser(getUserFromSession(refreshedSession))
        } else {
          setSession(initialSession)
          setUser(getUserFromSession(initialSession))
        }
      } else {
        setSession(initialSession)
        setUser(getUserFromSession(initialSession))
      }

      // If a session already exists (e.g., from a previous visit),
      // sync auth cookies so middleware/SSR sees the authenticated user
      // BEFORE we mark auth loading as finished, to avoid race with API calls.
      if (initialSession && initialSession.access_token !== lastSyncedSessionId) {
        lastSyncedSessionId = initialSession.access_token
        try {
          await fetch('/auth/events', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ event: 'SIGNED_IN', session: initialSession }),
          })
        } catch (e) {
          console.warn('Initial auth cookie sync failed:', e)
        }
      }

      setLoading(false)
      console.log('DEBUG AuthContext: Session initialization complete')
      isInitialLoad = false
    })()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      // Skip the initial SIGNED_IN event that fires on page load
      if (isInitialLoad && event === 'SIGNED_IN') {
        return
      }

      setSession((session as unknown as MinimalSession | null))
      setUser((session as any)?.user ?? null)
      setLoading(false)

      // Log security events
      if (event === 'SIGNED_IN' && session?.user) {
        // This will be logged by the session security manager
        console.log('User signed in, session security active')
      } else if (event === 'SIGNED_OUT') {
        console.log('User signed out')
      }

      // Only sync if this is a real auth state change, not a duplicate
      // and only if the session ID has changed
      const sessionId = session?.access_token || null
      if ((event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') &&
          sessionId !== lastSyncedSessionId) {
        lastSyncedSessionId = sessionId
        try {
          await fetch('/auth/events', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ event, session }),
          })
        } catch (e) {
          console.warn('Failed to sync auth cookies:', e)
        }
      }
    })

    return () => subscription.unsubscribe()
  }, [supabase])

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        loading,
        signOut,
        sessionSecurity,
      }}
    >
      {children}

      {/* Session timeout warning dialog */}
      <SessionTimeoutDialog
        show={timeoutDialog.showDialog}
        timeoutInfo={timeoutDialog.timeoutInfo}
        onExtendSession={() => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          sessionSecurity.extendSession()
          timeoutDialog.extendSession()
        }}
        onLogoutNow={() => {
          sessionSecurity.forceLogout()
          timeoutDialog.logoutNow()
        }}
        onClose={() => timeoutDialog.extendSession()}
      />
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
