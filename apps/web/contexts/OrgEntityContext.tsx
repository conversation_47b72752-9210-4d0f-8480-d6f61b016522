'use client'

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { listUserTenants, listUserEntities } from '@belbooks/dal'
import type { Database } from '@belbooks/types'
import type { UserContextResponse } from '@/lib/api-types'
import { parseJsonSafe } from '@/lib/http/api'

type UserTenant = Database['public']['Views']['v_user_tenants']['Row'] & {
  tenant_kind?: string | null // Fallback for old column name
}
type UserEntity = Database['public']['Views']['v_user_entities']['Row']

interface OrgEntitySelection {
  tenantId: number | null
  entityId: number | null
  tenantName?: string
  entityName?: string
  orgType?: 'sme' | 'firm'
  mode: 'tenant' | 'entity'
}

interface OrgEntityContextType {
  selection: OrgEntitySelection
  tenants: UserTenant[]
  entities: UserEntity[]
  loading: boolean
  error: string | null

  // Context data
  userContext: UserContextResponse | null

  // UX helpers
  isSME: boolean
  isFirm: boolean
  shouldShowEntitySelector: boolean
  shouldShowOrgSelector: boolean

  // Actions
  selectTenant: (tenant: UserTenant | null) => void
  selectEntity: (entity: UserEntity | null) => void
  refreshData: () => Promise<void>
}

const OrgEntityContext = createContext<OrgEntityContextType | undefined>(
  undefined
)

const STORAGE_KEY = 'ledgerly_org_entity_selection'

function getStoredSelection(): OrgEntitySelection | null {
  if (typeof window === 'undefined') return null

  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return stored ? JSON.parse(stored) : null
  } catch {
    return null
  }
}

// Track last fetch time to avoid rapid refetching
let lastFetchTime = 0
const FETCH_COOLDOWN_MS = 2000 // Minimum 2 seconds between fetches

// Fetch user context from the new API endpoint
async function fetchUserContext(): Promise<UserContextResponse | null> {
  // Throttle requests to avoid rapid refetching
  const now = Date.now()
  if (now - lastFetchTime < FETCH_COOLDOWN_MS) {
    console.log('Skipping user context fetch - too soon after last fetch')
    return null
  }
  lastFetchTime = now

  const maxAttempts = 3
  let attempt = 0
  let delayMs = 300

  while (attempt < maxAttempts) {
    try {
      const response = await fetch('/api/me/context', {
        method: 'GET',
        credentials: 'same-origin',
      })
      if (response.status === 429) {
        const retryAfter = Number(response.headers.get('Retry-After') || '0')
        const wait = retryAfter > 0 ? retryAfter * 1000 : delayMs
        await new Promise(res => setTimeout(res, wait))
        attempt++
        delayMs *= 2
        continue
      }

      if (!response.ok) {
        const errorText = await response.text()
        console.error(
          'fetchUserContext: API error:',
          response.status,
          errorText
        )
        throw new Error(`Failed to fetch user context: ${response.status}`)
      }
      const result = await parseJsonSafe<UserContextResponse>(response)
      console.log('fetchUserContext: API response:', result)
      return result ?? null
    } catch (error) {
      if (attempt >= maxAttempts - 1) {
        console.error('Error fetching user context:', error)
        return null
      }
      await new Promise(res => setTimeout(res, delayMs))
      attempt++
      delayMs *= 2
    }
  }
  return null
}

function storeSelection(selection: OrgEntitySelection) {
  if (typeof window === 'undefined') return

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(selection))
  } catch (error) {
    console.warn('Failed to store org/entity selection:', error)
  }
}

export function OrgEntityProvider({ children }: { children: React.ReactNode }) {
  const [selection, setSelection] = useState<OrgEntitySelection>(() => {
    return (
      getStoredSelection() || {
        tenantId: null,
        entityId: null,
        mode: 'tenant',
      }
    )
  })

  const [tenants, setTenants] = useState<UserTenant[]>([])
  const [entities, setEntities] = useState<UserEntity[]>([])
  const [userContext, setUserContext] = useState<UserContextResponse | null>(
    null
  )
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { user, loading: authLoading } = useAuth()

  const loadData = useCallback(async () => {
    console.log('OrgEntityContext loadData called:', {
      authLoading,
      user: !!user,
    })
    // Defer until auth finishes resolving to avoid redirect loops
    if (authLoading) return
    if (!user) {
      console.log('OrgEntityContext: No user, setting loading to false')
      setLoading(false)
      return
    }
    try {
      console.log('OrgEntityContext: Loading data for user:', user.id)
      setLoading(true)
      setError(null)

      // Ensure we have a Supabase client for data fetching (session is managed by AuthContext)
      const { createBrowserClient } = await import('@belbooks/dal')
      const supabase = createBrowserClient()

      const [tenantsData, entitiesData, contextData] = await Promise.all([
        listUserTenants(),
        listUserEntities(),
        fetchUserContext(),
      ])

      console.log('OrgEntityContext: Data loaded:', {
        tenantsData: tenantsData?.length || 0,
        entitiesData: entitiesData?.length || 0,
        contextData,
      })

      // If we have no tenants from the DAL but we have context data, create a synthetic tenant
      let finalTenants = tenantsData || []
      if (finalTenants.length === 0 && contextData?.tenant) {
        // Create a synthetic tenant from context data
        finalTenants = [
          {
            user_id: user.id,
            tenant_id: contextData.tenant.id,
            role: contextData.tenant.role,
            tenant_name: contextData.tenant.name,
            tenant_kind: null,
            org_type: contextData.tenant.org_type,
            created_at: new Date().toISOString(),
          },
        ]
        console.log(
          'OrgEntityContext: Created synthetic tenant from context data'
        )
      }

      setTenants(finalTenants)
      setUserContext(contextData)

      // Use entities from userContext if available, otherwise fall back to entitiesData
      let finalEntities = entitiesData || []
      if (contextData?.entities && contextData.entities.length > 0) {
        // Convert userContext entities to the expected format
        finalEntities = contextData.entities.map(entity => ({
          user_id: null, // Not needed for display
          entity_id: entity.id,
          tenant_id: contextData.tenant?.id || null,
          role: entity.role,
          entity_name: entity.name,
          tenant_name: contextData.tenant?.name || null,
          created_at: null,
        }))
        console.log('OrgEntityContext: Using entities from userContext')
      } else {
        console.log('OrgEntityContext: Using entities from DAL')
      }

      setEntities(finalEntities)

      // Debug logging
      console.log('OrgEntityContext loaded data:', {
        tenantsCount: tenantsData?.length || 0,
        entitiesCount: finalEntities?.length || 0,
        contextTenant: contextData?.tenant?.name,
        contextOrgType: contextData?.tenant?.org_type,
        contextEntitiesCount: contextData?.entities?.length || 0,
        contextEntities: contextData?.entities?.map(e => e.name),
        finalEntitiesCount: finalEntities.length,
      })

      // For SME users, auto-select the single entity
      if (
        contextData?.tenant?.org_type === 'sme' &&
        contextData.entities.length > 0
      ) {
        const smeEntity = contextData.entities[0]
        const newSelection: OrgEntitySelection = {
          tenantId: contextData.tenant.id,
          entityId: smeEntity.id,
          tenantName: contextData.tenant.name,
          entityName: smeEntity.name,
          orgType: 'sme',
          mode: 'entity',
        }
        setSelection(prev => {
          // Only update if actually different to avoid loops
          if (
            prev.entityId !== newSelection.entityId ||
            prev.tenantId !== newSelection.tenantId
          ) {
            storeSelection(newSelection)
            return newSelection
          }
          return prev
        })
        return
      }

      // If there's exactly one entity available overall, auto-select it
      // This covers cases where userContext failed (e.g., cookie sync race)
      const currentSelection = getStoredSelection()
      if (
        !currentSelection?.entityId &&
        finalEntities &&
        finalEntities.length === 1
      ) {
        const only = finalEntities[0]
        const owningTenant = tenantsData?.find(
          t => t.tenant_id === only.tenant_id
        )
        const newSelection: OrgEntitySelection = {
          tenantId: only.tenant_id ?? null,
          entityId: only.entity_id ?? null,
          tenantName:
            only.tenant_name ?? owningTenant?.tenant_name ?? undefined,
          entityName: only.entity_name ?? undefined,
          orgType: (owningTenant?.org_type ||
            owningTenant?.tenant_kind?.toLowerCase()) as
            | 'sme'
            | 'firm'
            | undefined,
          mode: 'entity',
        }
        setSelection(prev => {
          // Only update if actually different to avoid loops
          if (
            prev.entityId !== newSelection.entityId ||
            prev.tenantId !== newSelection.tenantId
          ) {
            storeSelection(newSelection)
            return newSelection
          }
          return prev
        })
        return
      }

      // Auto-select first tenant if none selected and user has tenants
      const storedSelection = getStoredSelection()
      if (!storedSelection?.tenantId && tenantsData && tenantsData.length > 0) {
        const firstTenant = tenantsData[0]
        const newSelection: OrgEntitySelection = {
          tenantId: firstTenant.tenant_id,
          entityId: null,
          tenantName: firstTenant.tenant_name ?? undefined,
          orgType: (firstTenant.org_type ||
            firstTenant.tenant_kind?.toLowerCase() ||
            'sme') as 'sme' | 'firm',
          mode: 'tenant' as const,
        }
        setSelection(prev => {
          if (prev.tenantId !== newSelection.tenantId) {
            storeSelection(newSelection)
            return newSelection
          }
          return prev
        })
      }

      // Validate stored selection still exists
      const validationSelection = getStoredSelection()
      if (validationSelection?.tenantId) {
        const tenantExists = tenantsData?.some(
          (t: UserTenant) => t.tenant_id === validationSelection.tenantId
        )
        if (!tenantExists) {
          setSelection(prev => {
            if (prev.tenantId !== null) {
              const newSelection = {
                tenantId: null,
                entityId: null,
                mode: 'tenant' as const,
              }
              storeSelection(newSelection)
              return newSelection
            }
            return prev
          })
        }
      }

      if (validationSelection?.entityId) {
        const entityExists = finalEntities?.some(
          (e: UserEntity) => e.entity_id === validationSelection.entityId
        )
        if (!entityExists) {
          setSelection(prev => {
            if (prev.entityId !== null) {
              const newSelection = {
                ...prev,
                entityId: null,
                entityName: undefined,
                mode: 'tenant' as const,
              }
              storeSelection(newSelection)
              return newSelection
            }
            return prev
          })
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
      console.error('Error loading org/entity data:', err)
    } finally {
      setLoading(false)
    }
  }, [authLoading, user]) // Removed 'selection' from dependencies to avoid loops

  const selectTenant = useCallback((tenant: UserTenant | null) => {
    const newSelection: OrgEntitySelection = {
      tenantId: tenant?.tenant_id || null,
      entityId: null, // Reset entity when switching tenants
      tenantName: tenant?.tenant_name ?? undefined,
      entityName: undefined,
      orgType: (tenant?.org_type ||
        tenant?.tenant_kind?.toLowerCase() ||
        'sme') as 'sme' | 'firm',
      mode: 'tenant',
    }

    setSelection(newSelection)
    storeSelection(newSelection)
  }, [])

  const selectEntity = useCallback(
    (entity: UserEntity | null) => {
      setSelection(currentSelection => {
        const tenant = tenants.find(t => t.tenant_id === entity?.tenant_id)
        const newSelection: OrgEntitySelection = {
          tenantId: entity?.tenant_id || currentSelection.tenantId,
          entityId: entity?.entity_id || null,
          tenantName: entity?.tenant_name ?? currentSelection.tenantName,
          entityName: entity?.entity_name ?? undefined,
          orgType: (tenant?.org_type ||
            tenant?.tenant_kind?.toLowerCase() ||
            'sme') as 'sme' | 'firm',
          mode: 'entity',
        }

        storeSelection(newSelection)
        return newSelection
      })
    },
    [tenants]
  )

  // Use a ref to track if we've already loaded data for this user
  const loadedUserRef = useRef<string | null>(null)

  useEffect(() => {
    // Only load data if auth is not loading and user has changed
    if (authLoading) return

    const userId = user?.id || null
    if (loadedUserRef.current === userId) return

    loadedUserRef.current = userId

    // Debounce the loadData call to avoid rapid refetching
    const timeoutId = setTimeout(() => {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      loadData()
    }, 100) // Small delay to batch updates

    return () => clearTimeout(timeoutId)
  }, [authLoading, user?.id, loadData])

  // Calculate UX helpers with fallback logic
  // If org_type is not available, determine based on entity count
  let orgType = userContext?.tenant?.org_type || selection.orgType

  // Fallback: if no org_type but we have data, infer from entity count
  if (!orgType && tenants.length > 0) {
    const firstTenant = tenants[0]
    // Check if org_type exists in tenant data
    if (firstTenant.org_type) {
      orgType = firstTenant.org_type as 'sme' | 'firm'
    } else if (firstTenant.tenant_kind) {
      // Fallback to old column name and normalize to lowercase
      orgType = (firstTenant.tenant_kind as string).toLowerCase() as
        | 'sme'
        | 'firm'
    } else {
      // Final fallback: infer from entity count (1 entity = SME, multiple = firm)
      const entitiesForTenant = entities.filter(
        e => e.tenant_id === firstTenant.tenant_id
      )
      orgType = entitiesForTenant.length <= 1 ? 'sme' : 'firm'
    }
  }

  // Default to SME if we still don't have an org type
  if (!orgType) {
    orgType = 'sme'
  }

  const isSME = orgType === 'sme'
  const isFirm = orgType === 'firm'
  // For SME users, never show entity selector. For others, show if firm or multiple entities
  const shouldShowEntitySelector =
    !isSME && (isFirm || (userContext?.entities?.length || entities.length) > 1)
  const shouldShowOrgSelector = tenants.length > 1 && !isSME

  // Debug logging for UX helpers (only when loading state changes)
  const debugInfoRef = useRef<string>('')
  const currentDebugInfo = JSON.stringify({
    isSME,
    isFirm,
    shouldShowEntitySelector,
    shouldShowOrgSelector,
    orgType: orgType,
    userAuthenticated: !!user,
    loading: loading,
  })

  if (debugInfoRef.current !== currentDebugInfo) {
    debugInfoRef.current = currentDebugInfo
    console.log('UX helpers calculated:', {
      isSME,
      isFirm,
      shouldShowEntitySelector,
      shouldShowOrgSelector,
      orgType: orgType,
      detectedOrgType: orgType,
      rawOrgType: userContext?.tenant?.org_type,
      entitiesFromContext: userContext?.entities?.length || 0,
      entitiesFromDAL: entities.length,
      userContextTenant: userContext?.tenant,
      userAuthenticated: !!user,
      loading: loading,
    })
  }

  const contextValue: OrgEntityContextType = useMemo(
    () => ({
      selection,
      tenants,
      entities,
      userContext,
      loading,
      error,
      isSME,
      isFirm,
      shouldShowEntitySelector,
      shouldShowOrgSelector,
      selectTenant,
      selectEntity,
      refreshData: loadData,
    }),
    [
      selection,
      tenants,
      entities,
      userContext,
      loading,
      error,
      isSME,
      isFirm,
      shouldShowEntitySelector,
      shouldShowOrgSelector,
      selectTenant,
      selectEntity,
      // Removed loadData from dependencies to prevent infinite re-renders
    ]
  )

  return (
    <OrgEntityContext.Provider value={contextValue}>
      {children}
    </OrgEntityContext.Provider>
  )
}

export function useOrgEntity() {
  const context = useContext(OrgEntityContext)
  if (context === undefined) {
    throw new Error('useOrgEntity must be used within an OrgEntityProvider')
  }
  return context
}

// Convenience hooks
export function useCurrentTenant() {
  const { selection, tenants } = useOrgEntity()
  return tenants.find(t => t.tenant_id === selection.tenantId) || null
}

export function useCurrentEntity() {
  const { selection, entities } = useOrgEntity()
  return entities.find(e => e.entity_id === selection.entityId) || null
}

export function useEntitiesForTenant(tenantId: number | null) {
  const { entities } = useOrgEntity()
  return entities.filter(e => e.tenant_id === tenantId)
}
