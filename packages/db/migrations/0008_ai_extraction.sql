-- Migration: AI Extraction Shadow Mode Support
-- Adds columns and feature flag for Gemini AI extraction with shadow mode comparison

-- Add shadow mode columns to inbox_documents
ALTER TABLE inbox_documents ADD COLUMN IF NOT EXISTS extraction_alt JSONB;
ALTER TABLE inbox_documents ADD COLUMN IF NOT EXISTS confidence_alt NUMERIC(3,2);
ALTER TABLE inbox_documents ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Add extraction processing metadata columns
ALTER TABLE inbox_documents ADD COLUMN IF NOT EXISTS text_length INTEGER;
ALTER TABLE inbox_documents ADD COLUMN IF NOT EXISTS processing_time NUMERIC(6,3);
ALTER TABLE inbox_documents ADD COLUMN IF NOT EXISTS extraction_provider TEXT DEFAULT 'stub';

-- Update status enum to include validation states  
-- (PostgreSQL doesn't support ALTER TYPE ADD VALUE in a transaction by default,
-- so we'll handle this with constraints for now)
ALTER TABLE inbox_documents ADD CONSTRAINT check_extraction_status 
    CHECK (status IN ('uploaded', 'extracted', 'suggested', 'confirmed', 'posted', 'exported', 'failed', 'validation_error'));

-- <PERSON>reate indexes for shadow mode queries
CREATE INDEX IF NOT EXISTS idx_inbox_documents_provider_confidence 
    ON inbox_documents(extraction_provider, confidence DESC) 
    WHERE status IN ('extracted', 'suggested');

CREATE INDEX IF NOT EXISTS idx_inbox_documents_metadata_gin 
    ON inbox_documents USING GIN (metadata) 
    WHERE metadata IS NOT NULL;

-- Insert AIExtractGeminiEnabled feature flag for entities that want to use Gemini
-- (This will be enabled per entity as needed)
INSERT INTO feature_flags (entity_id, feature_name, enabled, created_at, updated_at)
SELECT 
    e.id,
    'AIExtractGeminiEnabled',
    false,  -- Start disabled for all entities
    NOW(),
    NOW()
FROM entities e
WHERE NOT EXISTS (
    SELECT 1 FROM feature_flags ff 
    WHERE ff.entity_id = e.id AND ff.feature_name = 'AIExtractGeminiEnabled'
);

-- Create function to check if AI extraction is enabled for an entity
CREATE OR REPLACE FUNCTION is_ai_extraction_enabled(entity_id_param BIGINT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS(
        SELECT 1 FROM feature_flags 
        WHERE entity_id = entity_id_param 
        AND feature_name = 'AIExtractGeminiEnabled' 
        AND enabled = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment for documentation
COMMENT ON FUNCTION is_ai_extraction_enabled(BIGINT) IS 
'Check if Gemini AI extraction is enabled for the specified entity';

-- Add extraction comparison view for analysis
CREATE OR REPLACE VIEW v_extraction_comparison AS
SELECT 
    id,
    entity_id,
    status,
    extraction_provider,
    confidence,
    confidence_alt,
    CASE 
        WHEN confidence_alt IS NOT NULL THEN confidence - confidence_alt
        ELSE NULL 
    END as confidence_diff,
    text_length,
    processing_time,
    created_at,
    updated_at,
    -- Flags for analysis
    (confidence >= 0.7) as primary_high_confidence,
    (confidence_alt >= 0.7) as shadow_high_confidence,
    (extraction IS NOT NULL) as has_primary_extraction,
    (extraction_alt IS NOT NULL) as has_shadow_extraction
FROM inbox_documents
WHERE status IN ('extracted', 'suggested', 'confirmed');

-- Add RLS policies for new columns (inherit from table's existing policies)
-- The existing inbox_documents policies will automatically cover new columns

-- Grant permissions to service role for the new function
GRANT EXECUTE ON FUNCTION is_ai_extraction_enabled(BIGINT) TO service_role;

-- Add extraction statistics function for monitoring
CREATE OR REPLACE FUNCTION get_extraction_stats(entity_id_param BIGINT DEFAULT NULL)
RETURNS TABLE(
    extraction_provider TEXT,
    total_extractions BIGINT,
    avg_confidence NUMERIC,
    high_confidence_count BIGINT,
    avg_processing_time NUMERIC,
    success_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.extraction_provider,
        COUNT(*) as total_extractions,
        ROUND(AVG(d.confidence), 3) as avg_confidence,
        COUNT(*) FILTER (WHERE d.confidence >= 0.7) as high_confidence_count,
        ROUND(AVG(d.processing_time), 3) as avg_processing_time,
        ROUND(
            COUNT(*) FILTER (WHERE d.status IN ('extracted', 'suggested', 'confirmed'))::NUMERIC / 
            NULLIF(COUNT(*), 0), 3
        ) as success_rate
    FROM inbox_documents d
    WHERE (entity_id_param IS NULL OR d.entity_id = entity_id_param)
    AND d.status != 'uploaded'
    GROUP BY d.extraction_provider
    ORDER BY total_extractions DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_extraction_stats(BIGINT) TO service_role;

COMMENT ON FUNCTION get_extraction_stats(BIGINT) IS 
'Get extraction performance statistics by provider, optionally filtered by entity';

-- Add trigger to update metadata timestamps
CREATE OR REPLACE FUNCTION update_extraction_metadata()
RETURNS TRIGGER AS $$
BEGIN
    -- Update metadata when extraction fields change
    IF (OLD.extraction IS DISTINCT FROM NEW.extraction) OR 
       (OLD.confidence IS DISTINCT FROM NEW.confidence) OR
       (OLD.status IS DISTINCT FROM NEW.status) THEN
        
        -- Add processing timestamp to metadata
        NEW.metadata = COALESCE(NEW.metadata, '{}'::jsonb) || 
                       jsonb_build_object('last_extraction_update', NOW());
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_update_extraction_metadata
    BEFORE UPDATE ON inbox_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_extraction_metadata();