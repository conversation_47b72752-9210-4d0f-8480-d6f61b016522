/**
 * Security Monitoring Module
 * Provides comprehensive security event logging, analysis, and monitoring
 */

/* eslint-disable @typescript-eslint/unbound-method */
import {
  storeSecurityEvent,
  getSecurityEvents,
  getSecurityEventStats,
  detectSuspiciousIPs,
  getFailedLoginsByIP,
  getRateLimitViolationsByIP,
  shouldBlockIP,
  type SecurityEventInput,
  type SecurityEvent as DALSecurityEvent,
  type SecurityEventType,
  type SecurityEventStats,
  type SuspiciousIP,
} from '@belbooks/dal'

// Legacy interface for backward compatibility
export interface SecurityEvent {
  type:
    | 'session_timeout'
    | 'failed_auth'
    | 'suspicious_activity'
    | 'session_extended'
    | 'admin_action'
    | 'csrf_violation'
    | 'permission_denied'
  timestamp: number
  userId?: string
  details?: Record<string, unknown>
}

// Enhanced security event interface for database persistence
export interface EnhancedSecurityEvent {
  event_type: SecurityEventType
  user_id?: string
  session_id?: string
  ip_address?: string
  user_agent?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  resource?: string
  action?: string
  details?: Record<string, unknown>
}

// Camel case interface for easier usage
export interface CamelCaseSecurityEvent {
  type: SecurityEventType
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  resource?: string
  action?: string
  details?: Record<string, unknown>
}

/**
 * Convert camelCase event to snake_case for database storage
 */
function normalizeSecurityEvent(
  event: EnhancedSecurityEvent | CamelCaseSecurityEvent
): SecurityEventInput {
  // Check if it's camelCase format
  const isCamelCase =
    'type' in event ||
    'ipAddress' in event ||
    'userAgent' in event ||
    'userId' in event ||
    'sessionId' in event

  let normalized: SecurityEventInput

  if (isCamelCase) {
    const camelEvent = event
    normalized = {
      event_type: camelEvent.type,
      user_id: camelEvent.userId,
      session_id: camelEvent.sessionId,
      ip_address: camelEvent.ipAddress,
      user_agent: camelEvent.userAgent,
      severity: camelEvent.severity,
      resource: camelEvent.resource,
      action: camelEvent.action,
      details: camelEvent.details,
    }
  } else {
    const snakeEvent = event
    normalized = {
      event_type: snakeEvent.event_type,
      user_id: snakeEvent.user_id,
      session_id: snakeEvent.session_id,
      ip_address: snakeEvent.ip_address,
      user_agent: snakeEvent.user_agent,
      severity: snakeEvent.severity,
      resource: snakeEvent.resource,
      action: snakeEvent.action,
      details: snakeEvent.details,
    }
  }

  // Handle null values - convert to undefined or appropriate defaults
  if (normalized.session_id === null) normalized.session_id = undefined
  if (normalized.action === null) normalized.action = undefined
  if (normalized.details === null) normalized.details = {}

  return normalized
}

/**
 * Main function to log security events with database persistence
 */
export async function logSecurityEvent(
  event: EnhancedSecurityEvent | CamelCaseSecurityEvent
): Promise<void> {
  try {
    const normalizedEvent = normalizeSecurityEvent(event)

    // Handle unknown IP addresses - convert to undefined and clear details
    if (
      normalizedEvent.ip_address === 'unknown' ||
      normalizedEvent.ip_address === '::1' ||
      normalizedEvent.ip_address === '127.0.0.1'
    ) {
      normalizedEvent.ip_address = undefined
      normalizedEvent.user_agent =
        normalizedEvent.user_agent === 'unknown'
          ? undefined
          : normalizedEvent.user_agent
      normalizedEvent.details = {}
      normalizedEvent.resource = undefined
      normalizedEvent.action = undefined
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('🔒 Security Event:', {
        type: normalizedEvent.event_type,
        severity: normalizedEvent.severity,
        ip: normalizedEvent.ip_address,
        user: normalizedEvent.user_id,
        details: normalizedEvent.details,
      })
    }

    // Store in database
    await storeSecurityEvent(normalizedEvent)
  } catch (error) {
    console.error('Failed to store security event in database:', error)
  }
}

/**
 * Request-like object for extracting security information
 */
interface RequestLike {
  ip?: string
  headers?: Record<string, string | string[] | undefined> | Headers
  connection?: {
    remoteAddress?: string
  }
}

/**
 * Extract request information for security logging
 */
export function extractRequestInfo(request: RequestLike): {
  ip_address: string
  user_agent: string
} {
  // Handle both Headers and Record types
  const getHeader = (key: string): string | undefined => {
    if (!request?.headers) return undefined
    if (request.headers instanceof Headers) {
      return request.headers.get(key) || undefined
    }
    // eslint-disable-next-line security/detect-object-injection
    const value = request.headers[key]
    return Array.isArray(value) ? value[0] : value
  }

  const forwardedFor = getHeader('x-forwarded-for')

  const ip_address =
    request?.ip ||
    forwardedFor?.split(',')[0]?.trim() ||
    getHeader('x-real-ip') ||
    request?.connection?.remoteAddress ||
    'unknown'

  const userAgent = getHeader('user-agent')
  const user_agent = userAgent || 'unknown'

  return { ip_address, user_agent }
}

/**
 * Security Events Helper Functions
 * Provides convenient methods for logging common security events
 */
export const SecurityEvents = {
  // Event type constants
  SESSION_TIMEOUT: 'session_timeout' as const,
  FAILED_AUTH: 'failed_auth' as const,
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_REQUEST' as const,
  SESSION_EXTENDED: 'session_extended' as const,
  ADMIN_ACTION: 'ADMIN_ACTION' as const,
  CSRF_VIOLATION: 'CSRF_VIOLATION' as const,
  PERMISSION_DENIED: 'PERMISSION_DENIED' as const,

  // Helper methods for common security events
  async failedLogin(
    email: string,
    ip_address: string,
    user_agent: string,
    reason: string
  ): Promise<void> {
    await logSecurityEvent({
      event_type: 'AUTH_FAILED_LOGIN',
      ip_address,
      user_agent,
      severity: 'medium',
      resource: 'auth/login',
      action: 'login_attempt',
      details: {
        email,
        reason,
        timestamp: new Date().toISOString(),
      },
    })
  },

  async successfulLogin(
    user_id: string,
    session_id: string,
    ip_address: string,
    user_agent: string
  ): Promise<void> {
    await logSecurityEvent({
      event_type: 'AUTH_SUCCESSFUL_LOGIN',
      user_id,
      session_id,
      ip_address,
      user_agent,
      severity: 'low',
      resource: 'auth/login',
      action: 'login_success',
      details: {},
    })
  },

  async csrfViolation(
    user_id: string,
    ip_address: string,
    user_agent: string,
    resource: string
  ): Promise<void> {
    await logSecurityEvent({
      event_type: 'CSRF_VIOLATION',
      user_id,
      ip_address,
      user_agent,
      severity: 'high',
      resource,
      action: 'csrf_violation',
      details: {},
    })
  },

  async rateLimitExceeded(
    identifier: string,
    resource: string,
    ip_address: string,
    user_agent: string
  ): Promise<void> {
    await logSecurityEvent({
      event_type: 'RATE_LIMIT_EXCEEDED',
      ip_address,
      user_agent,
      severity: 'medium',
      resource,
      action: 'rate_limit_exceeded',
      details: {
        identifier,
        endpoint: resource,
      },
    })
  },

  async adminAction(
    user_id: string,
    action: string,
    target: string,
    ip_address: string,
    user_agent: string
  ): Promise<void> {
    await logSecurityEvent({
      event_type: 'ADMIN_ACTION',
      user_id,
      ip_address,
      user_agent,
      severity: 'high',
      resource: 'admin',
      action: 'admin_action',
      details: {
        action,
        target,
      },
    })
  },

  async dataExport(
    user_id: string,
    export_type: string,
    record_count: number,
    ip_address: string,
    user_agent: string
  ): Promise<void> {
    // Determine severity based on export size
    const severity = record_count > 1000 ? 'high' : 'medium'

    await logSecurityEvent({
      event_type: 'DATA_EXPORT',
      user_id,
      ip_address,
      user_agent,
      severity,
      resource: 'data/export',
      action: 'export_data',
      details: {
        dataType: export_type,
        recordCount: record_count,
      },
    })
  },

  async suspiciousRequest(
    user_id: string,
    reason: string,
    ip_address: string,
    user_agent: string,
    details?: Record<string, unknown>
  ): Promise<void> {
    await logSecurityEvent({
      event_type: 'SUSPICIOUS_REQUEST',
      user_id,
      ip_address,
      user_agent,
      severity: 'high',
      resource: 'request',
      action: 'suspicious_activity',
      details: {
        reason,
        ...details,
      },
    })
  },
}

// Transformed security event for API responses
export interface TransformedSecurityEvent {
  type: SecurityEventType
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  resource?: string
  action?: string
  details?: Record<string, unknown>
  timestamp: Date
}

/**
 * Transform database security event to camelCase format
 */
function transformSecurityEvent(
  event: DALSecurityEvent
): TransformedSecurityEvent {
  return {
    type: event.event_type,
    userId: event.user_id || undefined,
    sessionId: event.session_id || undefined,
    ipAddress: event.ip_address || undefined,
    userAgent: event.user_agent || undefined,
    severity: event.severity,
    resource: event.resource || undefined,
    action: event.action || undefined,
    details: event.details || {},
    timestamp: new Date(event.created_at),
  }
}

/**
 * Security Analyzer
 * Provides analysis and querying capabilities for security events
 */
export const SecurityAnalyzer = {
  /**
   * Get failed login attempts by IP address
   */
  async getFailedLoginsByIP(
    ip_address: string,
    time_window_minutes: number
  ): Promise<number> {
    try {
      return await getFailedLoginsByIP(ip_address, time_window_minutes)
    } catch (error) {
      console.error('Failed to get failed logins by IP:', error)
      return 0
    }
  },

  /**
   * Get rate limit violations by IP address
   */
  async getRateLimitViolationsByIP(
    ip_address: string,
    time_window_minutes: number
  ): Promise<number> {
    try {
      return await getRateLimitViolationsByIP(ip_address, time_window_minutes)
    } catch (error) {
      console.error('Failed to get rate limit violations by IP:', error)
      return 0
    }
  },

  /**
   * Check if an IP should be blocked
   */
  async shouldBlockIP(ip_address: string): Promise<boolean> {
    try {
      return await shouldBlockIP(ip_address)
    } catch (error) {
      console.error('Failed to check if IP should be blocked:', error)
      return false
    }
  },

  /**
   * Get high severity events
   */
  async getHighSeverityEvents(
    time_window_hours: number
  ): Promise<TransformedSecurityEvent[]> {
    try {
      const events = await getSecurityEvents({
        severity: ['high', 'critical'],
        time_window_hours,
      })
      return events.map(transformSecurityEvent)
    } catch (error) {
      console.error('Failed to get high severity events:', error)
      return []
    }
  },

  /**
   * Get suspicious IP addresses
   */
  async getSuspiciousIPs(
    time_window_minutes: number,
    failed_login_threshold: number,
    rate_limit_threshold: number
  ): Promise<SuspiciousIP[]> {
    try {
      return await detectSuspiciousIPs(
        time_window_minutes,
        failed_login_threshold,
        rate_limit_threshold
      )
    } catch (error) {
      console.error('Failed to get suspicious IPs:', error)
      return []
    }
  },

  /**
   * Get security event statistics
   */
  async getEventStats(
    time_window_hours: number,
    event_types?: SecurityEventType[]
  ): Promise<SecurityEventStats[]> {
    try {
      return await getSecurityEventStats(time_window_hours, event_types)
    } catch (error) {
      console.error('Failed to get event statistics:', error)
      return []
    }
  },
}

/**
 * Legacy SecurityMonitor class for backward compatibility
 * @deprecated Use logSecurityEvent and SecurityAnalyzer instead
 */
export class SecurityMonitor {
  private events: SecurityEvent[] = []
  private maxEvents = 1000

  public logEvent(event: Omit<SecurityEvent, 'timestamp'>) {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: Date.now(),
    }

    this.events.push(securityEvent)

    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Security Event:', securityEvent)
    }

    // In production, you might want to send this to a monitoring service
    this.sendToMonitoringService(securityEvent)
  }

  private sendToMonitoringService(_event: SecurityEvent) {
    // In a real application, you would send this to your monitoring service
    // For now, we'll just store it locally
    try {
      // Example: await fetch('/api/security/events', { method: 'POST', body: JSON.stringify(event) })
    } catch (error) {
      console.error('Failed to send security event:', error)
    }
  }

  public getRecentEvents(limit = 50): SecurityEvent[] {
    return this.events.slice(-limit)
  }

  public getEventsByType(type: SecurityEvent['type']): SecurityEvent[] {
    return this.events.filter(event => event.type === type)
  }

  public clearEvents() {
    this.events = []
  }
}

export const securityMonitor = new SecurityMonitor()

// Export types for external use
export type {
  SecurityEventInput,
  SecurityEvent as DALSecurityEvent,
  SecurityEventType,
  SecurityEventStats,
  SuspiciousIP,
} from '@belbooks/dal'
