import { createSecureBrowserClient } from '@/lib/session-security'

export type AccountInfo = { id: number; code: string; name: string }

const BFF_BASE_URL = process.env.NEXT_PUBLIC_BFF_URL || 'http://localhost:4000'

const _cache = new Map<
  number,
  { ts: number; data: Map<number, { code: string; name: string }> }
>()
const _inflight = new Map<
  number,
  Promise<Map<number, { code: string; name: string }>>
>()
const TTL_MS = 60_000

export async function fetchAccountsMap(
  entityId: number
): Promise<Map<number, { code: string; name: string }>> {
  // Serve from fresh cache
  const c = _cache.get(entityId)
  const now = Date.now()
  if (c && now - c.ts < TTL_MS) {
    return c.data
  }
  // Coalesce concurrent requests
  const existing = _inflight.get(entityId)
  if (existing) return existing

  const promise = (async () => {
    try {
      const supabase = createSecureBrowserClient()
      const { data: sessionData } = await supabase.auth.getSession()
      const token = sessionData.session?.access_token

      const response = await fetch(
        `${BFF_BASE_URL}/entities/${entityId}/accounts`,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Key': process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY || '',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        }
      )

      const map = new Map<number, { code: string; name: string }>()
      if (!response.ok) return map

      const result: unknown = await response.json()
      // Safely pluck `data` field from unknown
      const data =
        typeof result === 'object' && result !== null && 'data' in result
          ? (result as { data?: unknown }).data
          : undefined
      if (Array.isArray(data)) {
        for (const item of data as Array<Record<string, unknown>>) {
          const id = item.id
          const code = item.code
          const name = item.name
          if (
            typeof id === 'number' &&
            typeof code === 'string' &&
            typeof name === 'string'
          ) {
            map.set(id, { code, name })
          }
        }
      }
      _cache.set(entityId, { ts: Date.now(), data: map })
      return map
    } finally {
      _inflight.delete(entityId)
    }
  })()

  _inflight.set(entityId, promise)
  return promise
}

export function getAccountDisplayName(
  accounts: Map<number, { code: string; name: string }>,
  accountId?: number
) {
  if (!accountId) return 'Account undefined'
  const a = accounts.get(accountId)
  return a ? `${a.code} - ${a.name}` : `Account ${accountId}`
}
