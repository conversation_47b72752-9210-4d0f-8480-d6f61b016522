#!/bin/bash

# dev-setup.sh - Comprehensive development environment startup script for ledgerly
# Usage: ./dev-setup.sh [--check|--stop|--help]

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Service configuration (compatible with Bash 3.2)
# Format: "service_name:port:command:description"
SERVICES=(
    "frontend:3000:pnpm dev --filter=@belbooks/web:Frontend (Next.js)"
    "bff:4000:pnpm dev --filter=@belbooks/bff:BFF (Backend for Frontend)"
    "workers-py:8000:make -C apps/workers-py run-dev:Workers-py (FastAPI)"
    "celery-worker:0:make -C apps/workers-py run-worker:Celery Worker"
    "redis:6379:redis-server:Redis Server"
    "supabase:54321:supabase start:Supabase (PostgreSQL + API)"
)

# Global variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/.dev-logs"
PID_DIR="$SCRIPT_DIR/.dev-pids"
TIMEOUT=30
VERBOSE=false

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${CYAN}🔍 $1${NC}"
    fi
}

print_header() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                  Ledgerly Dev Environment                    ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  Comprehensive startup script for all development services  ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Helper functions for service configuration
get_service_info() {
    local service_name=$1
    local field=$2  # name, port, cmd, or desc

    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r name port cmd desc <<< "$service_config"
        if [[ "$name" == "$service_name" ]]; then
            case "$field" in
                "name") echo "$name" ;;
                "port") echo "$port" ;;
                "cmd") echo "$cmd" ;;
                "desc") echo "$desc" ;;
            esac
            return 0
        fi
    done
    return 1
}

get_all_service_names() {
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r name port cmd desc <<< "$service_config"
        echo "$name"
    done
}

is_valid_service() {
    local service_name=$1
    get_service_info "$service_name" "name" >/dev/null 2>&1
}

show_help() {
    print_header
    echo "Usage: $0 [OPTIONS] [SERVICES...]"
    echo ""
    echo "OPTIONS:"
    echo "  --check, -c     Only check service status, don't start anything"
    echo "  --stop, -s      Stop all running services"
    echo "  --verbose, -v   Enable verbose output"
    echo "  --timeout=N     Set timeout for service startup (default: 30s)"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "SERVICES (optional, defaults to all):"
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r name port cmd desc <<< "$service_config"
        echo "  $name$(printf '%*s' $((15-${#name})) '') $desc"
    done
    echo ""
    echo "EXAMPLES:"
    echo "  $0                    # Start all services"
    echo "  $0 --check           # Check status of all services"
    echo "  $0 --stop            # Stop all services"
    echo "  $0 frontend bff      # Start only frontend and BFF"
    echo "  $0 --verbose         # Start with verbose logging"
    echo ""
}

# Utility functions
create_directories() {
    mkdir -p "$LOG_DIR" "$PID_DIR"
}

cleanup_on_exit() {
    log_info "Cleaning up..."
    # Kill any background processes we started
    if [[ -d "$PID_DIR" ]]; then
        for pidfile in "$PID_DIR"/*.pid; do
            if [[ -f "$pidfile" ]]; then
                local pid=$(cat "$pidfile" 2>/dev/null || echo "")
                if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
                    log_debug "Killing process $pid"
                    kill "$pid" 2>/dev/null || true
                fi
                rm -f "$pidfile"
            fi
        done
    fi
}

trap cleanup_on_exit EXIT

# Port checking functions
is_port_in_use() {
    local port=$1
    if [[ "$port" == "0" ]]; then
        return 1  # No port to check
    fi

    if command -v lsof >/dev/null 2>&1; then
        lsof -i ":$port" >/dev/null 2>&1
    elif command -v netstat >/dev/null 2>&1; then
        netstat -ln | grep ":$port " >/dev/null 2>&1
    else
        # Fallback: try to connect
        timeout 1 bash -c "</dev/tcp/localhost/$port" 2>/dev/null
    fi
}

get_port_pid() {
    local port=$1
    if [[ "$port" == "0" ]]; then
        echo ""
        return
    fi

    if command -v lsof >/dev/null 2>&1; then
        lsof -ti ":$port" 2>/dev/null | head -1
    else
        echo ""
    fi
}

# Service health checking functions
check_http_endpoint() {
    local url=$1
    local timeout=${2:-5}

    if command -v curl >/dev/null 2>&1; then
        curl -s --max-time "$timeout" --fail "$url" >/dev/null 2>&1
    elif command -v wget >/dev/null 2>&1; then
        wget -q --timeout="$timeout" --tries=1 -O /dev/null "$url" 2>/dev/null
    else
        return 1
    fi
}

check_redis() {
    if command -v redis-cli >/dev/null 2>&1; then
        redis-cli ping 2>/dev/null | grep -q "PONG"
    else
        # Fallback: check if port is open
        is_port_in_use 6379
    fi
}

check_postgres() {
    local db_url="${DATABASE_URL:-postgresql://postgres:postgres@127.0.0.1:54322/postgres}"

    if command -v psql >/dev/null 2>&1; then
        psql "$db_url" -c "SELECT 1;" >/dev/null 2>&1
    else
        # Fallback: check if Supabase port is open
        is_port_in_use 54322
    fi
}

check_service_health() {
    local service=$1

    case "$service" in
        "frontend")
            check_http_endpoint "http://localhost:3000" 5
            ;;
        "bff")
            check_http_endpoint "http://localhost:4000/health" 5
            ;;
        "workers-py")
            check_http_endpoint "http://localhost:8000/health" 5
            ;;
        "celery-worker")
            # Check if celery worker process is running
            pgrep -f "celery.*worker" >/dev/null 2>&1
            ;;
        "redis")
            check_redis
            ;;
        "supabase")
            check_postgres && check_http_endpoint "http://127.0.0.1:54321/rest/v1/" 5
            ;;
        *)
            return 1
            ;;
    esac
}

get_service_status() {
    local service=$1
    local port=$(get_service_info "$service" "port")
    local desc=$(get_service_info "$service" "desc")

    local status="❌ Stopped"
    local pid=""
    local health="❌ Unhealthy"

    # Check if port is in use (for services with ports)
    if [[ "$port" != "0" ]] && is_port_in_use "$port"; then
        status="🟡 Running"
        pid=$(get_port_pid "$port")

        # Check health
        if check_service_health "$service"; then
            status="✅ Running"
            health="✅ Healthy"
        else
            health="⚠️  Unhealthy"
        fi
    elif [[ "$port" == "0" ]]; then
        # For services without ports (like celery worker)
        if check_service_health "$service"; then
            status="✅ Running"
            health="✅ Healthy"
            pid=$(pgrep -f "celery.*worker" | head -1 || echo "")
        fi
    fi

    echo "$status|$health|$pid"
}

print_service_status() {
    local service=$1
    local port=$(get_service_info "$service" "port")
    local desc=$(get_service_info "$service" "desc")
    IFS='|' read -r status health pid <<< "$(get_service_status "$service")"

    local port_info=""
    if [[ "$port" != "0" ]]; then
        port_info=" (port $port)"
    fi

    local pid_info=""
    if [[ -n "$pid" ]]; then
        pid_info=" [PID: $pid]"
    fi

    printf "  %-15s %s %s%s%s\n" "$service" "$status" "$desc" "$port_info" "$pid_info"
    if [[ "$VERBOSE" == "true" ]]; then
        printf "  %-15s %s Health check\n" "" "$health"
    fi
}

check_all_services() {
    log_info "Checking service status..."
    echo ""

    for service_name in $(get_all_service_names); do
        print_service_status "$service_name"
    done

    echo ""
}

# Prerequisites checking
check_prerequisites() {
    log_info "Checking prerequisites..."

    local missing_tools=()

    # Check for required tools
    if ! command -v pnpm &> /dev/null; then
        missing_tools+=("pnpm")
    fi

    if ! command -v supabase &> /dev/null; then
        missing_tools+=("supabase")
    fi

    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi

    if ! command -v uv &> /dev/null; then
        log_warning "uv not found - Python workers may not work"
    fi

    if ! command -v redis-server &> /dev/null; then
        log_warning "redis-server not found - will try to start via Docker"
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        echo ""
        echo "Please install missing tools:"
        for tool in "${missing_tools[@]}"; do
            case $tool in
                "pnpm")
                    echo "  pnpm: npm install -g pnpm"
                    ;;
                "supabase")
                    echo "  supabase: brew install supabase/tap/supabase (macOS) or see https://supabase.com/docs/guides/cli"
                    ;;
                "docker")
                    echo "  docker: https://docs.docker.com/desktop/"
                    ;;
            esac
        done
        return 1
    fi

    log_success "Prerequisites check passed"
    return 0
}

# Environment setup
setup_environment() {
    log_info "Setting up environment..."

    # Check if .env.local exists
    if [[ ! -f ".env.local" ]]; then
        if [[ -f ".env.example" ]]; then
            cp .env.example .env.local
            log_success ".env.local created from .env.example"
            log_warning "Please review and update .env.local with your configuration"
        else
            log_error ".env.example not found - cannot create .env.local"
            return 1
        fi
    else
        log_debug ".env.local already exists"
    fi

    # Source environment variables
    if [[ -f ".env.local" ]]; then
        set -a
        source .env.local
        set +a
        log_debug "Environment variables loaded from .env.local"
    fi

    return 0
}

# Service starting functions
start_service() {
    local service=$1
    local port=$(get_service_info "$service" "port")
    local cmd=$(get_service_info "$service" "cmd")
    local desc=$(get_service_info "$service" "desc")

    log_info "Starting $service ($desc)..."

    # Check if already running and healthy
    if [[ "$(get_service_status "$service" | cut -d'|' -f1)" == "✅ Running" ]]; then
        log_success "$service is already running and healthy"
        return 0
    fi

    # Kill existing process if port is in use but unhealthy
    if [[ "$port" != "0" ]] && is_port_in_use "$port"; then
        local existing_pid=$(get_port_pid "$port")
        if [[ -n "$existing_pid" ]]; then
            log_warning "Killing existing process on port $port (PID: $existing_pid)"
            kill "$existing_pid" 2>/dev/null || true
            sleep 2
        fi
    fi

    # Service-specific startup logic
    case "$service" in
        "supabase")
            start_supabase
            ;;
        "redis")
            start_redis
            ;;
        "frontend"|"bff")
            start_node_service "$service" "$cmd"
            ;;
        "workers-py"|"celery-worker")
            start_python_service "$service" "$cmd"
            ;;
        *)
            log_error "Unknown service: $service"
            return 1
            ;;
    esac
}

start_supabase() {
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker Desktop and try again."
        return 1
    fi

    # Start Supabase
    log_debug "Running: supabase start"
    if supabase start >"$LOG_DIR/supabase.log" 2>&1; then
        log_success "Supabase started successfully"

        # Wait for services to be ready
        log_info "Waiting for Supabase services to be ready..."
        local attempts=0
        while [[ $attempts -lt $TIMEOUT ]]; do
            if check_service_health "supabase"; then
                log_success "Supabase is healthy"
                return 0
            fi
            sleep 1
            ((attempts++))
        done

        log_warning "Supabase started but health check failed"
        return 1
    else
        log_error "Failed to start Supabase (check $LOG_DIR/supabase.log)"
        return 1
    fi
}

start_redis() {
    if command -v redis-server >/dev/null 2>&1; then
        log_debug "Starting Redis server..."
        redis-server --daemonize yes --logfile "$LOG_DIR/redis.log" --pidfile "$PID_DIR/redis.pid"

        # Wait for Redis to be ready
        local attempts=0
        while [[ $attempts -lt $TIMEOUT ]]; do
            if check_redis; then
                log_success "Redis started successfully"
                return 0
            fi
            sleep 1
            ((attempts++))
        done

        log_error "Redis failed to start properly"
        return 1
    else
        log_warning "redis-server not found, trying Docker..."
        docker run -d --name ledgerly-redis -p 6379:6379 redis:7-alpine >"$LOG_DIR/redis-docker.log" 2>&1

        # Wait for Redis to be ready
        local attempts=0
        while [[ $attempts -lt $TIMEOUT ]]; do
            if check_redis; then
                log_success "Redis (Docker) started successfully"
                return 0
            fi
            sleep 1
            ((attempts++))
        done

        log_error "Redis (Docker) failed to start properly"
        return 1
    fi
}

start_node_service() {
    local service=$1
    local cmd=$2

    log_debug "Starting $service with command: $cmd"

    # Start the service in background
    nohup bash -c "$cmd" >"$LOG_DIR/$service.log" 2>&1 &
    local pid=$!
    echo "$pid" > "$PID_DIR/$service.pid"

    log_debug "$service started with PID: $pid"

    # Wait for service to be ready
    log_info "Waiting for $service to be ready..."
    local attempts=0
    while [[ $attempts -lt $TIMEOUT ]]; do
        if check_service_health "$service"; then
            log_success "$service is ready"
            return 0
        fi

        # Check if process is still running
        if ! kill -0 "$pid" 2>/dev/null; then
            log_error "$service process died (check $LOG_DIR/$service.log)"
            return 1
        fi

        sleep 1
        ((attempts++))
    done

    log_error "$service failed to become ready within ${TIMEOUT}s"
    return 1
}

start_python_service() {
    local service=$1
    local cmd=$2

    # Check if we're in the workers-py directory or need to change to it
    local work_dir="$SCRIPT_DIR"
    if [[ "$cmd" == make* ]]; then
        work_dir="$SCRIPT_DIR"
    fi

    log_debug "Starting $service with command: $cmd (in $work_dir)"

    # Start the service in background
    cd "$work_dir"
    nohup bash -c "$cmd" >"$LOG_DIR/$service.log" 2>&1 &
    local pid=$!
    echo "$pid" > "$PID_DIR/$service.pid"
    cd "$SCRIPT_DIR"

    log_debug "$service started with PID: $pid"

    # Wait for service to be ready
    log_info "Waiting for $service to be ready..."
    local attempts=0
    while [[ $attempts -lt $TIMEOUT ]]; do
        if check_service_health "$service"; then
            log_success "$service is ready"
            return 0
        fi

        # Check if process is still running
        if ! kill -0 "$pid" 2>/dev/null; then
            log_error "$service process died (check $LOG_DIR/$service.log)"
            return 1
        fi

        sleep 1
        ((attempts++))
    done

    log_error "$service failed to become ready within ${TIMEOUT}s"
    return 1
}

# Service stopping functions
stop_service() {
    local service=$1
    local port=$(get_service_info "$service" "port")

    log_info "Stopping $service..."

    # Check if service is running
    local status=$(get_service_status "$service" | cut -d'|' -f1)
    if [[ "$status" == "❌ Stopped" ]]; then
        log_success "$service is already stopped"
        return 0
    fi

    case "$service" in
        "supabase")
            stop_supabase
            ;;
        "redis")
            stop_redis
            ;;
        *)
            stop_generic_service "$service" "$port"
            ;;
    esac
}

stop_supabase() {
    log_debug "Running: supabase stop"
    if supabase stop >"$LOG_DIR/supabase-stop.log" 2>&1; then
        log_success "Supabase stopped successfully"
    else
        log_warning "Failed to stop Supabase gracefully"
    fi
}

stop_redis() {
    # Try to stop Redis server
    if [[ -f "$PID_DIR/redis.pid" ]]; then
        local pid=$(cat "$PID_DIR/redis.pid" 2>/dev/null || echo "")
        if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm -f "$PID_DIR/redis.pid"
            log_success "Redis server stopped"
        fi
    fi

    # Try to stop Docker Redis
    if docker ps --format "table {{.Names}}" | grep -q "ledgerly-redis"; then
        docker stop ledgerly-redis >/dev/null 2>&1
        docker rm ledgerly-redis >/dev/null 2>&1
        log_success "Redis (Docker) stopped"
    fi
}

stop_generic_service() {
    local service=$1
    local port=$2

    # Try to stop via PID file first
    if [[ -f "$PID_DIR/$service.pid" ]]; then
        local pid=$(cat "$PID_DIR/$service.pid" 2>/dev/null || echo "")
        if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid" 2>/dev/null || true
            fi
            rm -f "$PID_DIR/$service.pid"
            log_success "$service stopped (PID: $pid)"
            return 0
        fi
    fi

    # Fallback: kill by port
    if [[ "$port" != "0" ]] && is_port_in_use "$port"; then
        local pid=$(get_port_pid "$port")
        if [[ -n "$pid" ]]; then
            kill "$pid" 2>/dev/null || true
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid" 2>/dev/null || true
            fi
            log_success "$service stopped (port $port, PID: $pid)"
        fi
    else
        log_success "$service was not running"
    fi
}

stop_all_services() {
    log_info "Stopping all services..."

    # Stop in reverse dependency order
    local stop_order=("celery-worker" "workers-py" "bff" "frontend" "redis" "supabase")

    for service in "${stop_order[@]}"; do
        if is_valid_service "$service"; then
            stop_service "$service"
        fi
    done

    # Clean up log and pid directories
    rm -rf "$PID_DIR"/*.pid 2>/dev/null || true

    log_success "All services stopped"
}

# Main startup logic
start_all_services() {
    local services_to_start=("$@")

    # If no specific services provided, start all
    if [[ ${#services_to_start[@]} -eq 0 ]]; then
        services_to_start=("supabase" "redis" "workers-py" "celery-worker" "bff" "frontend")
    fi

    log_info "Starting services: ${services_to_start[*]}"

    # Check prerequisites
    if ! check_prerequisites; then
        return 1
    fi

    # Setup environment
    if ! setup_environment; then
        return 1
    fi

    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing dependencies..."
        pnpm install
    fi

    # Start services in dependency order
    local failed_services=()

    for service in "${services_to_start[@]}"; do
        if ! is_valid_service "$service"; then
            log_error "Unknown service: $service"
            failed_services+=("$service")
            continue
        fi

        if ! start_service "$service"; then
            failed_services+=("$service")
        fi
    done

    echo ""
    log_info "Startup complete!"

    # Show final status
    check_all_services

    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_error "Failed to start: ${failed_services[*]}"
        echo ""
        log_info "Check log files in $LOG_DIR/ for details"
        return 1
    fi

    # Show useful information
    echo ""
    log_success "🎉 Development environment is ready!"
    echo ""
    echo -e "${BLUE}Available services:${NC}"
    echo "  • Frontend:    http://localhost:3000"
    echo "  • BFF API:     http://localhost:4000"
    echo "  • Workers API: http://localhost:8000"
    echo "  • Supabase:    http://127.0.0.1:54323 (Studio)"
    echo ""
    echo -e "${BLUE}Useful commands:${NC}"
    echo "  $0 --check           # Check service status"
    echo "  $0 --stop            # Stop all services"
    echo "  tail -f $LOG_DIR/*.log  # View service logs"
    echo ""

    return 0
}

# Argument parsing and main function
main() {
    local action="start"
    local services=()

    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --check|-c)
                action="check"
                shift
                ;;
            --stop|-s)
                action="stop"
                shift
                ;;
            --verbose|-v)
                VERBOSE=true
                shift
                ;;
            --timeout=*)
                TIMEOUT="${1#*=}"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            --*)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                # Check if it's a valid service name
                if is_valid_service "$1"; then
                    services+=("$1")
                else
                    log_error "Unknown service: $1"
                    echo "Available services: $(get_all_service_names | tr '\n' ' ')"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Create necessary directories
    create_directories

    # Execute action
    case "$action" in
        "check")
            print_header
            check_all_services
            ;;
        "stop")
            print_header
            stop_all_services
            ;;
        "start")
            print_header
            if [[ ${#services[@]} -gt 0 ]]; then
                start_all_services "${services[@]}"
            else
                start_all_services
            fi
            ;;
        *)
            log_error "Unknown action: $action"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"