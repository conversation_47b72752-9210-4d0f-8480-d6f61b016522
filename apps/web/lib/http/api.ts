export type ApiEnvelope<T = unknown> = {
  success?: boolean
  data?: T
  error?: string
}

export type ApiError = { error?: string }

export async function parseJsonSafe<T = unknown>(
  res: Response
): Promise<T | undefined> {
  try {
    // Some endpoints might return 204 or empty body
    const text = await res.text()
    if (!text) return undefined
    return JSON.parse(text) as T
  } catch {
    return undefined
  }
}

export function isApiError(u: unknown): u is ApiError {
  return (
    typeof u === 'object' &&
    u !== null &&
    (!('error' in (u as Record<string, unknown>)) ||
      typeof (u as Record<string, unknown>).error === 'string')
  )
}

export function getApiErrorMessage(u: unknown, fallback: string): string {
  if (isApiError(u) && u.error) return u.error
  return fallback
}

export function isApiSuccessWithData<T extends object>(
  u: unknown,
  keys: (keyof T)[]
): u is ApiEnvelope<T> {
  if (typeof u !== 'object' || u === null) return false
  const obj = u as Record<string, unknown>
  if ('success' in obj && typeof obj.success !== 'boolean') return false
  if ('error' in obj && typeof obj.error !== 'string') return false
  if (!('data' in obj)) return false
  const data = obj.data as Record<string, unknown> | undefined
  if (!data) return false
  return keys.every(k => k in data)
}

export function extractData<T = unknown>(u: unknown): T | undefined {
  if (typeof u !== 'object' || u === null) return undefined
  const obj = u as { data?: unknown }
  return obj.data as T | undefined
}
