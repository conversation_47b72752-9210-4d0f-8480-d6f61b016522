'use client'

import React from 'react'
import { ZenIcons } from '@/components/ZenIcons'

// Zen UI Theme
const zenTheme = {
  bg: '#FBFAF5',
  surface: '#FFFFFC',
  primaryText: '#1a1a1a',
  secondaryText: '#6b7280',
  subtleText: '#9ca3af',
  border: '#f3f4f6',
  borderHover: '#e5e7eb',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  primaryAction: '#3b82f6',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)',
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
}

interface Change {
  field: string
  label: string
  oldValue: string
  newValue: string
}

interface ChangeConfirmationModalProps {
  isOpen: boolean
  changes: Change[]
  onConfirm: () => void
  onCancel: () => void
  loading?: boolean
}

export default function ChangeConfirmationModal({
  isOpen,
  changes,
  onConfirm,
  onCancel,
  loading = false,
}: ChangeConfirmationModalProps) {
  if (!isOpen) return null

  const getOverlayStyle = () => ({
    position: 'fixed' as const,
    inset: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    padding: '16px',
  })

  const getModalStyle = () => ({
    backgroundColor: zenTheme.surface,
    borderRadius: '12px',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    maxWidth: '600px',
    width: '100%',
    maxHeight: '80vh',
    overflow: 'auto',
    position: 'relative' as const,
  })

  const getHeaderStyle = () => ({
    padding: '24px 24px 16px 24px',
    borderBottom: `1px solid ${zenTheme.border}`,
  })

  const getTitleStyle = () => ({
    fontSize: '20px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    margin: 0,
    marginBottom: '8px',
  })

  const getSubtitleStyle = () => ({
    fontSize: '14px',
    color: zenTheme.secondaryText,
    margin: 0,
  })

  const getContentStyle = () => ({
    padding: '24px',
  })

  const getFooterStyle = () => ({
    padding: '16px 24px 24px 24px',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
    borderTop: `1px solid ${zenTheme.border}`,
  })

  const getButtonStyle = (variant: 'primary' | 'secondary') => ({
    padding: '8px 16px',
    borderRadius: '6px',
    fontSize: '14px',
    fontWeight: 500,
    cursor: loading ? 'not-allowed' : 'pointer',
    border: variant === 'primary' ? 'none' : `1px solid ${zenTheme.border}`,
    backgroundColor: variant === 'primary' 
      ? (loading ? zenTheme.subtleText : zenTheme.primaryAction)
      : 'transparent',
    color: variant === 'primary' ? 'white' : zenTheme.primaryText,
    opacity: loading ? 0.7 : 1,
    transition: 'all 0.15s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
  })

  const getChangeItemStyle = () => ({
    padding: '16px',
    border: `1px solid ${zenTheme.border}`,
    borderRadius: '8px',
    marginBottom: '12px',
    backgroundColor: zenTheme.bg,
  })

  const getChangeFieldStyle = () => ({
    fontSize: '14px',
    fontWeight: 600,
    color: zenTheme.primaryText,
    marginBottom: '8px',
  })

  const getChangeValueStyle = (type: 'old' | 'new') => ({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '14px',
    marginBottom: type === 'old' ? '4px' : '0',
  })

  const getValueLabelStyle = (type: 'old' | 'new') => ({
    minWidth: '60px',
    fontSize: '12px',
    fontWeight: 500,
    color: type === 'old' ? zenTheme.error : zenTheme.success,
    textTransform: 'uppercase' as const,
  })

  const getValueTextStyle = (type: 'old' | 'new') => ({
    color: type === 'old' ? zenTheme.secondaryText : zenTheme.primaryText,
    fontWeight: type === 'new' ? 500 : 400,
    textDecoration: type === 'old' ? 'line-through' : 'none',
  })

  return (
    <div style={getOverlayStyle()}>
      <div style={getModalStyle()}>
        {/* Header */}
        <div style={getHeaderStyle()}>
          <h2 style={getTitleStyle()}>Confirm Changes</h2>
          <p style={getSubtitleStyle()}>
            You have made {changes.length} change{changes.length !== 1 ? 's' : ''} to this document. Please review before confirming.
          </p>
        </div>

        {/* Content */}
        <div style={getContentStyle()}>
          {changes.length === 0 ? (
            <div style={{
              textAlign: 'center',
              color: zenTheme.secondaryText,
              padding: '32px 16px',
            }}>
              <div style={{ marginBottom: '8px' }}>✓</div>
              <div>No changes detected</div>
            </div>
          ) : (
            <div>
              {changes.map((change, index) => (
                <div key={index} style={getChangeItemStyle()}>
                  <div style={getChangeFieldStyle()}>{change.label}</div>
                  
                  <div style={getChangeValueStyle('old')}>
                    <span style={getValueLabelStyle('old')}>From:</span>
                    <span style={getValueTextStyle('old')}>
                      {change.oldValue || '(empty)'}
                    </span>
                  </div>
                  
                  <div style={getChangeValueStyle('new')}>
                    <span style={getValueLabelStyle('new')}>To:</span>
                    <span style={getValueTextStyle('new')}>
                      {change.newValue || '(empty)'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div style={getFooterStyle()}>
          <button
            onClick={onCancel}
            disabled={loading}
            style={getButtonStyle('secondary')}
          >
            Cancel
          </button>
          
          <button
            onClick={onConfirm}
            disabled={loading}
            style={getButtonStyle('primary')}
          >
            {loading ? (
              <>
                <span>⏳</span>
                Confirming...
              </>
            ) : (
              <>
                {ZenIcons.review({ size: 14 })}
                Confirm Changes
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
