import { useState, useEffect, useCallback } from 'react'
import type { TenantSubscription, UsageMetrics } from '@belbooks/types'
import { useAuth } from '@/contexts/AuthContext'

// Mock subscription data for development
const MOCK_SUBSCRIPTION: TenantSubscription = {
  tenant_id: 1,
  status: 'trial',
  plan: 'trial',
  current_period_start: new Date(),
  current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  limits: {
    entities: 1,
    users: 2,
    transactions_per_month: 100,
    storage_gb: 1,
    features: ['basic_ledger', 'bank_import'],
  },
  trial_ends_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
}

const MOCK_USAGE: UsageMetrics = {
  tenant_id: 1,
  period_start: new Date(),
  period_end: new Date(),
  entities_used: 0,
  users_active: 1,
  transactions_count: 0,
  storage_used_gb: 0.01,
}

export interface SubscriptionHook {
  subscription: TenantSubscription | null
  usage: UsageMetrics | null
  loading: boolean
  error: string | null
  isFeatureEnabled: (feature: string) => boolean
  isWithinLimits: () => boolean
  refreshSubscription: () => Promise<void>
}

export const useSubscription = (tenantId?: number): SubscriptionHook => {
  const [subscription, setSubscription] = useState<TenantSubscription | null>(
    null
  )
  const [usage, setUsage] = useState<UsageMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { user } = useAuth()

  const fetchSubscription = useCallback(async () => {
    if (!user || !tenantId) {
      setSubscription(null)
      setUsage(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // TODO: Replace with actual API calls when subscription system is implemented
      // For now, return mock data
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setSubscription({
        ...MOCK_SUBSCRIPTION,
        tenant_id: tenantId,
      })
      setUsage({
        ...MOCK_USAGE,
        tenant_id: tenantId,
      })
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to load subscription'
      )
      setSubscription(null)
      setUsage(null)
    } finally {
      setLoading(false)
    }
  }, [user, tenantId])

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    fetchSubscription()
  }, [fetchSubscription])

  const isFeatureEnabled = (feature: string): boolean => {
    if (!subscription) return false
    return (
      subscription.limits.features.includes(feature) ||
      subscription.limits.features.includes('all_features')
    )
  }

  const isWithinLimits = (): boolean => {
    if (!subscription || !usage) return true

    const limits = subscription.limits

    // Check each limit (-1 means unlimited)
    if (limits.entities !== -1 && usage.entities_used > limits.entities) {
      return false
    }

    if (limits.users !== -1 && usage.users_active > limits.users) {
      return false
    }

    if (
      limits.transactions_per_month !== -1 &&
      usage.transactions_count > limits.transactions_per_month
    ) {
      return false
    }

    if (limits.storage_gb !== -1 && usage.storage_used_gb > limits.storage_gb) {
      return false
    }

    return true
  }

  const refreshSubscription = async () => {
    await fetchSubscription()
  }

  return {
    subscription,
    usage,
    loading,
    error,
    isFeatureEnabled,
    isWithinLimits,
    refreshSubscription,
  }
}
